package au.com.allianceautomation.iython.builtins.functions;

import java.util.List;

import au.com.allianceautomation.iython.builtins.AbstractBuiltinFunction;

/**
 * Python ascii() builtin function.
 * 
 * Returns a string containing a printable representation of an object,
 * but escape the non-ASCII characters in the string returned by repr()
 * using \\x, \\u or \\U escapes.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class AsciiFunction extends AbstractBuiltinFunction {
    
    public AsciiFunction() {
        super("ascii", 1, 1, "ascii(obj) -> str");
    }
    
    @Override
    protected Object execute(List<Object> args) {
        Object obj = args.get(0);
        
        // Get the string representation first
        String repr = pythonRepr(obj);
        
        // Escape non-ASCII characters
        StringBuilder result = new StringBuilder();
        for (int i = 0; i < repr.length(); i++) {
            char c = repr.charAt(i);
            if (c >= 32 && c <= 126) {
                // Printable ASCII character
                result.append(c);
            } else if (c <= 0xFF) {
                // Use \\x escape for characters up to 255
                result.append(String.format("\\\\x%02x", (int) c));
            } else if (c <= 0xFFFF) {
                // Use \\u escape for characters up to 65535
                result.append(String.format("\\\\u%04x", (int) c));
            } else {
                // Use \\U escape for higher Unicode characters
                result.append(String.format("\\\\U%08x", (int) c));
            }
        }
        
        return result.toString();
    }
    
    /**
     * Get the Python repr() representation of an object.
     */
    private String pythonRepr(Object obj) {
        if (obj == null) {
            return "None";
        } else if (obj instanceof String) {
            return "'" + obj.toString().replace("'", "\\'") + "'";
        } else if (obj instanceof Character) {
            return "'" + obj.toString() + "'";
        } else {
            return obj.toString();
        }
    }
}
