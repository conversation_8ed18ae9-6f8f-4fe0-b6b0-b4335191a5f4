package au.com.allianceautomation.iython.builtins.exceptions.arithmetic;

import au.com.allianceautomation.iython.builtins.exceptions.PythonException;
import java.util.List;

/**
 * Base class for those built-in exceptions that are raised for various arithmetic errors.
 * This corresponds to Python's ArithmeticError.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class PythonArithmeticError extends PythonException {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * Constructs a new PythonArithmeticError with no arguments.
     */
    public PythonArithmeticError() {
        super();
    }
    
    /**
     * Constructs a new PythonArithmeticError with the specified message.
     * 
     * @param message the detail message
     */
    public PythonArithmeticError(String message) {
        super(message);
    }
    
    /**
     * Constructs a new PythonArithmeticError with the specified arguments.
     * 
     * @param args the exception arguments
     */
    public PythonArithmeticError(Object... args) {
        super(args);
    }
    
    /**
     * Constructs a new PythonArithmeticError with the specified arguments.
     * 
     * @param args the exception arguments
     */
    public PythonArithmeticError(List<Object> args) {
        super(args);
    }
    
    @Override
    public String getPythonClassName() {
        return "ArithmeticError";
    }
}
