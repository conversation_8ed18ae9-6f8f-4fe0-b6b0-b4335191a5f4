package au.com.allianceautomation.iython.builtins.functions;

import au.com.allianceautomation.iython.builtins.AbstractBuiltinFunction;
import au.com.allianceautomation.iython.builtins.functions.BytesFunction.PythonBytes;
import au.com.allianceautomation.iython.builtins.functions.ByteArrayFunction.PythonByteArray;
import java.util.List;
import java.util.Arrays;

/**
 * Python memoryview() builtin function.
 * 
 * Returns a memory view object that exposes the buffer protocol.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class MemoryViewFunction extends AbstractBuiltinFunction {
    
    public MemoryViewFunction() {
        super("memoryview", 1, 1, "memoryview(obj) -> memoryview");
    }
    
    @Override
    protected Object execute(List<Object> args) {
        Object obj = args.get(0);
        
        if (obj instanceof PythonBytes) {
            PythonBytes bytes = (PythonBytes) obj;
            return new PythonMemoryView(bytes.getData(), false);
        } else if (obj instanceof PythonByteArray) {
            PythonByteArray byteArray = (PythonByteArray) obj;
            return new PythonMemoryView(byteArray.getData(), true);
        } else if (obj instanceof byte[]) {
            byte[] bytes = (byte[]) obj;
            return new PythonMemoryView(bytes, true);
        } else {
            throw new RuntimeException("TypeError: a bytes-like object is required, not '" + 
                                     obj.getClass().getSimpleName() + "'");
        }
    }
    
    /**
     * Memory view object for Python.
     */
    public static class PythonMemoryView {
        private final byte[] data;
        private final boolean writable;
        
        public PythonMemoryView(byte[] data, boolean writable) {
            this.data = writable ? data : data.clone();
            this.writable = writable;
        }
        
        public byte[] getData() {
            return data.clone();
        }
        
        public int length() {
            return data.length;
        }
        
        public boolean isWritable() {
            return writable;
        }
        
        public byte get(int index) {
            if (index < 0 || index >= data.length) {
                throw new RuntimeException("IndexError: index out of range");
            }
            return data[index];
        }
        
        public void set(int index, int value) {
            if (!writable) {
                throw new RuntimeException("TypeError: cannot modify read-only memory");
            }
            if (index < 0 || index >= data.length) {
                throw new RuntimeException("IndexError: index out of range");
            }
            if (value < 0 || value > 255) {
                throw new RuntimeException("ValueError: byte must be in range(0, 256)");
            }
            data[index] = (byte) value;
        }
        
        public PythonMemoryView slice(int start, int stop) {
            if (start < 0) start = 0;
            if (stop > data.length) stop = data.length;
            if (start >= stop) {
                return new PythonMemoryView(new byte[0], writable);
            }
            
            byte[] sliced = Arrays.copyOfRange(data, start, stop);
            return new PythonMemoryView(sliced, writable);
        }
        
        public PythonBytes toBytes() {
            return new PythonBytes(data);
        }
        
        public List<Integer> toList() {
            List<Integer> result = new java.util.ArrayList<>();
            for (byte b : data) {
                result.add(b & 0xFF);
            }
            return result;
        }
        
        @Override
        public String toString() {
            return "<memory at 0x" + Integer.toHexString(System.identityHashCode(this)) + ">";
        }
        
        @Override
        public boolean equals(Object obj) {
            if (this == obj) return true;
            if (!(obj instanceof PythonMemoryView)) return false;
            PythonMemoryView other = (PythonMemoryView) obj;
            return Arrays.equals(data, other.data);
        }
        
        @Override
        public int hashCode() {
            return Arrays.hashCode(data);
        }
    }
}
