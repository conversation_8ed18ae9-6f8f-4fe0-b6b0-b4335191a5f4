package au.com.allianceautomation.iython.builtins.exceptions;

import java.util.List;

/**
 * Raised when an operation or function is applied to an object of inappropriate type.
 * This corresponds to Python's TypeError.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class PythonTypeError extends PythonException {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * Constructs a new PythonTypeError with no arguments.
     */
    public PythonTypeError() {
        super();
    }
    
    /**
     * Constructs a new PythonTypeError with the specified message.
     * 
     * @param message the detail message
     */
    public PythonTypeError(String message) {
        super(message);
    }
    
    /**
     * Constructs a new PythonTypeError with the specified arguments.
     * 
     * @param args the exception arguments
     */
    public PythonTypeError(Object... args) {
        super(args);
    }
    
    /**
     * Constructs a new PythonTypeError with the specified arguments.
     * 
     * @param args the exception arguments
     */
    public PythonTypeError(List<Object> args) {
        super(args);
    }
    
    @Override
    public String getPythonClassName() {
        return "TypeError";
    }
}
