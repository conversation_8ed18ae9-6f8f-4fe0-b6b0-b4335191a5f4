package au.com.allianceautomation.iython.builtins.functions;

import au.com.allianceautomation.iython.builtins.AbstractBuiltinFunction;
import au.com.allianceautomation.iython.builtins.BuiltinFunction;
import java.util.List;

/**
 * Python property() builtin function.
 * 
 * Returns a property attribute for new-style classes (classes that derive from object).
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class PropertyFunction extends AbstractBuiltinFunction {
    
    public PropertyFunction() {
        super("property", 0, 4, "property(fget=None, fset=None, fdel=None, doc=None) -> property");
    }
    
    @Override
    protected Object execute(List<Object> args) {
        Object fget = args.size() > 0 ? args.get(0) : null;
        Object fset = args.size() > 1 ? args.get(1) : null;
        Object fdel = args.size() > 2 ? args.get(2) : null;
        Object doc = args.size() > 3 ? args.get(3) : null;
        
        return new PythonProperty(fget, fset, fdel, doc);
    }
    
    /**
     * Property descriptor for Python.
     */
    public static class PythonProperty {
        private final Object fget;
        private final Object fset;
        private final Object fdel;
        private final Object doc;
        
        public PythonProperty(Object fget, Object fset, Object fdel, Object doc) {
            this.fget = fget;
            this.fset = fset;
            this.fdel = fdel;
            this.doc = doc;
        }
        
        public Object getFget() {
            return fget;
        }
        
        public Object getFset() {
            return fset;
        }
        
        public Object getFdel() {
            return fdel;
        }
        
        public Object getDoc() {
            return doc;
        }
        
        public Object get(Object instance, Object owner) {
            if (fget == null) {
                throw new RuntimeException("AttributeError: unreadable attribute");
            }
            
            if (fget instanceof BuiltinFunction) {
                BuiltinFunction func = (BuiltinFunction) fget;
                return func.call(List.of(instance));
            } else {
                // For now, assume other callables are not supported
                throw new RuntimeException("TypeError: property getter not callable");
            }
        }
        
        public void set(Object instance, Object value) {
            if (fset == null) {
                throw new RuntimeException("AttributeError: can't set attribute");
            }
            
            if (fset instanceof BuiltinFunction) {
                BuiltinFunction func = (BuiltinFunction) fset;
                func.call(List.of(instance, value));
            } else {
                // For now, assume other callables are not supported
                throw new RuntimeException("TypeError: property setter not callable");
            }
        }
        
        public void delete(Object instance) {
            if (fdel == null) {
                throw new RuntimeException("AttributeError: can't delete attribute");
            }
            
            if (fdel instanceof BuiltinFunction) {
                BuiltinFunction func = (BuiltinFunction) fdel;
                func.call(List.of(instance));
            } else {
                // For now, assume other callables are not supported
                throw new RuntimeException("TypeError: property deleter not callable");
            }
        }
        
        public PythonProperty getter(Object fget) {
            return new PythonProperty(fget, this.fset, this.fdel, this.doc);
        }
        
        public PythonProperty setter(Object fset) {
            return new PythonProperty(this.fget, fset, this.fdel, this.doc);
        }
        
        public PythonProperty deleter(Object fdel) {
            return new PythonProperty(this.fget, this.fset, fdel, this.doc);
        }
        
        @Override
        public String toString() {
            return "<property object at 0x" + Integer.toHexString(System.identityHashCode(this)) + ">";
        }
    }
}
