package au.com.allianceautomation.iython.builtins.exceptions.arithmetic;

import java.util.List;

/**
 * Raised when the result of an arithmetic operation is too large to be represented.
 * This corresponds to Python's OverflowError.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class PythonOverflowError extends PythonArithmeticError {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * Constructs a new PythonOverflowError with no arguments.
     */
    public PythonOverflowError() {
        super();
    }
    
    /**
     * Constructs a new PythonOverflowError with the specified message.
     * 
     * @param message the detail message
     */
    public PythonOverflowError(String message) {
        super(message);
    }
    
    /**
     * Constructs a new PythonOverflowError with the specified arguments.
     * 
     * @param args the exception arguments
     */
    public PythonOverflowError(Object... args) {
        super(args);
    }
    
    /**
     * Constructs a new PythonOverflowError with the specified arguments.
     * 
     * @param args the exception arguments
     */
    public PythonOverflowError(List<Object> args) {
        super(args);
    }
    
    @Override
    public String getPythonClassName() {
        return "OverflowError";
    }
}
