package au.com.allianceautomation.iython.runtime.traceback;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * Represents a Python traceback containing a stack of execution frames.
 * Provides functionality to format and display tracebacks in CPython style.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class PythonTraceback {
    
    private final List<TracebackFrame> frames;
    
    /**
     * Creates a new empty traceback.
     */
    public PythonTraceback() {
        this.frames = new ArrayList<>();
    }
    
    /**
     * Creates a new traceback with the given frames.
     * 
     * @param frames the list of traceback frames
     */
    public PythonTraceback(List<TracebackFrame> frames) {
        this.frames = new ArrayList<>(frames);
    }
    
    /**
     * Adds a frame to the top of the traceback stack.
     * 
     * @param frame the frame to add
     */
    public void addFrame(TracebackFrame frame) {
        frames.add(frame);
    }
    
    /**
     * Adds a frame to the top of the traceback stack.
     * 
     * @param filename the filename
     * @param lineNumber the line number
     * @param functionName the function name
     * @param code the code line
     */
    public void addFrame(String filename, int lineNumber, String functionName, String code) {
        addFrame(new TracebackFrame(filename, lineNumber, functionName, code));
    }
    
    /**
     * Gets all frames in the traceback.
     * 
     * @return unmodifiable list of frames
     */
    public List<TracebackFrame> getFrames() {
        return Collections.unmodifiableList(frames);
    }
    
    /**
     * Gets the most recent (top) frame.
     * 
     * @return the most recent frame, or null if no frames
     */
    public TracebackFrame getMostRecentFrame() {
        return frames.isEmpty() ? null : frames.get(frames.size() - 1);
    }
    
    /**
     * Checks if the traceback is empty.
     * 
     * @return true if no frames are present
     */
    public boolean isEmpty() {
        return frames.isEmpty();
    }
    
    /**
     * Gets the number of frames in the traceback.
     * 
     * @return the frame count
     */
    public int size() {
        return frames.size();
    }
    
    /**
     * Formats the traceback for display in CPython style.
     * 
     * @return formatted traceback string
     */
    public String format() {
        if (frames.isEmpty()) {
            return "";
        }
        
        StringBuilder sb = new StringBuilder();
        sb.append("Traceback (most recent call last):\n");
        
        for (TracebackFrame frame : frames) {
            sb.append(frame.format()).append("\n");
        }
        
        return sb.toString();
    }
    
    /**
     * Creates a copy of this traceback.
     * 
     * @return a new traceback with the same frames
     */
    public PythonTraceback copy() {
        return new PythonTraceback(frames);
    }
    
    @Override
    public String toString() {
        return String.format("PythonTraceback(%d frames)", frames.size());
    }
}
