package au.com.allianceautomation.iython.builtins.functions;

import au.com.allianceautomation.iython.builtins.AbstractBuiltinFunction;
import java.util.List;
import java.util.HashMap;
import java.util.Map;
import java.lang.reflect.Field;

/**
 * Python vars() builtin function.
 * 
 * Returns the __dict__ attribute of an object. Without arguments, acts like locals().
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class VarsFunction extends AbstractBuiltinFunction {
    
    public VarsFunction() {
        super("vars", 0, 1, "vars([object]) -> dict");
    }
    
    @Override
    protected Object execute(List<Object> args) {
        if (args.isEmpty()) {
            // Without arguments, return locals()
            Map<String, Object> locals = new HashMap<>();
            locals.put("__name__", "__main__");
            locals.put("__doc__", null);
            locals.put("__package__", null);
            locals.put("__loader__", null);
            locals.put("__spec__", null);
            locals.put("__builtins__", au.com.allianceautomation.iython.builtins.BuiltinsModule.getInstance());
            return locals;
        }
        
        Object obj = args.get(0);
        if (obj == null) {
            throw new RuntimeException("TypeError: vars() argument must be an object with a __dict__ attribute");
        }
        
        // Try to get the object's attributes using reflection
        Map<String, Object> attributes = new HashMap<>();
        Class<?> clazz = obj.getClass();
        
        try {
            Field[] fields = clazz.getDeclaredFields();
            for (Field field : fields) {
                field.setAccessible(true);
                try {
                    Object value = field.get(obj);
                    attributes.put(field.getName(), value);
                } catch (IllegalAccessException e) {
                    // Skip inaccessible fields
                }
            }
        } catch (SecurityException e) {
            // If we can't access fields, return empty dict
        }
        
        return attributes;
    }
}
