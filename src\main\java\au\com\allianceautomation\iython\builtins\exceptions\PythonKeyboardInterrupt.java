package au.com.allianceautomation.iython.builtins.exceptions;

import java.util.List;

/**
 * Raised when the user hits the interrupt key (normally Control-C or Delete).
 * This corresponds to Python's KeyboardInterrupt.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class PythonKeyboardInterrupt extends PythonBaseException {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * Constructs a new PythonKeyboardInterrupt with no arguments.
     */
    public PythonKeyboardInterrupt() {
        super();
    }
    
    /**
     * Constructs a new PythonKeyboardInterrupt with the specified message.
     * 
     * @param message the detail message
     */
    public PythonKeyboardInterrupt(String message) {
        super(message);
    }
    
    /**
     * Constructs a new PythonKeyboardInterrupt with the specified arguments.
     * 
     * @param args the exception arguments
     */
    public PythonKeyboardInterrupt(Object... args) {
        super(args);
    }
    
    /**
     * Constructs a new PythonKeyboardInterrupt with the specified arguments.
     * 
     * @param args the exception arguments
     */
    public PythonKeyboardInterrupt(List<Object> args) {
        super(args);
    }
    
    @Override
    public String getPythonClassName() {
        return "KeyboardInterrupt";
    }
}
