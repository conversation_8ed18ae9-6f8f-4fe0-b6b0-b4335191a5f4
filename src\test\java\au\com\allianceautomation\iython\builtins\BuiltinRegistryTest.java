package au.com.allianceautomation.iython.builtins;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

/**
 * Test for BuiltinRegistry to verify all builtin functions are registered.
 */
public class BuiltinRegistryTest {
    
    @Test
    public void testBuiltinCount() {
        int count = BuiltinRegistry.getBuiltinCount();
        System.out.println("Total builtin functions registered: " + count);
        
        // We should have a significant number of builtin functions
        assertTrue(count > 50, "Should have more than 50 builtin functions");
    }
    
    @Test
    public void testSpecificBuiltins() {
        // Test some of the new functions we added
        assertTrue(BuiltinRegistry.isBuiltin("bytes"));
        assertTrue(BuiltinRegistry.isBuiltin("bytearray"));
        assertTrue(BuiltinRegistry.isBuiltin("complex"));
        assertTrue(BuiltinRegistry.isBuiltin("frozenset"));
        assertTrue(BuiltinRegistry.isBuiltin("memoryview"));
        assertTrue(BuiltinRegistry.isBuiltin("object"));
        assertTrue(BuiltinRegistry.isBuiltin("property"));
        assertTrue(BuiltinRegistry.isBuiltin("classmethod"));
        assertTrue(BuiltinRegistry.isBuiltin("staticmethod"));
        assertTrue(BuiltinRegistry.isBuiltin("slice"));
        assertTrue(BuiltinRegistry.isBuiltin("super"));
        assertTrue(BuiltinRegistry.isBuiltin("compile"));
        assertTrue(BuiltinRegistry.isBuiltin("delattr"));
        assertTrue(BuiltinRegistry.isBuiltin("help"));
    }
    
    @Test
    public void testBuiltinFunctionCall() {
        // Test that we can get and call a builtin function
        BuiltinFunction helpFunc = BuiltinRegistry.getBuiltin("help");
        assertNotNull(helpFunc);
        assertEquals("help", helpFunc.getName());
        
        // Test help function with no arguments (should not throw)
        assertDoesNotThrow(() -> helpFunc.call(java.util.Arrays.asList()));
    }
    
    @Test
    public void testPrintAllBuiltins() {
        System.out.println("\nAll registered builtin functions:");
        BuiltinRegistry.getBuiltinNames().stream()
            .sorted()
            .forEach(name -> {
                BuiltinFunction func = BuiltinRegistry.getBuiltin(name);
                System.out.println("  " + name + " - " + func.getDescription());
            });
    }
}
