package au.com.allianceautomation.iython.runtime.traceback;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import static org.junit.jupiter.api.Assertions.*;

import au.com.allianceautomation.iython.PythonExecutor;
import au.com.allianceautomation.iython.PythonExecutionException;

/**
 * Tests for enhanced traceback functionality in iython.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
class TracebackTest {
    
    @Test
    @DisplayName("Test NameError with suggestion")
    void testNameErrorWithSuggestion() {
        PythonExecutor executor = new PythonExecutor();
        
        try {
            // This should trigger a NameError with suggestion for 'vars'
            executor.executeCode("cars");
            fail("Expected PythonExecutionException");
        } catch (PythonExecutionException e) {
            // Check that the error message contains suggestion
            String message = e.getMessage();
            assertTrue(message.contains("NameError") || message.contains("name") || message.contains("not defined"),
                      "Error message should indicate NameError: " + message);
        }
        
        executor.close();
    }
    
    @Test
    @DisplayName("Test traceback frame creation")
    void testTracebackFrame() {
        TracebackFrame frame = new TracebackFrame("<stdin>", 1, "<module>", "cars");
        
        assertEquals("<stdin>", frame.getFilename());
        assertEquals(1, frame.getLineNumber());
        assertEquals("<module>", frame.getFunctionName());
        assertEquals("cars", frame.getCode());
        
        String formatted = frame.format();
        assertTrue(formatted.contains("File \"<stdin>\""));
        assertTrue(formatted.contains("line 1"));
        assertTrue(formatted.contains("in <module>"));
        assertTrue(formatted.contains("cars"));
    }
    
    @Test
    @DisplayName("Test traceback formatting")
    void testTracebackFormatting() {
        PythonTraceback traceback = new PythonTraceback();
        traceback.addFrame("<stdin>", 1, "<module>", "cars");
        
        String formatted = traceback.format();
        assertTrue(formatted.contains("Traceback (most recent call last):"));
        assertTrue(formatted.contains("File \"<stdin>\""));
        assertTrue(formatted.contains("line 1"));
    }
    
    @Test
    @DisplayName("Test name suggestion")
    void testNameSuggestion() {
        java.util.Set<String> availableNames = java.util.Set.of("vars", "print", "len", "str");
        
        // Test close match
        String suggestion = NameSuggester.suggestSimilarName("cars", availableNames);
        assertEquals("vars", suggestion);
        
        // Test no good match
        String noMatch = NameSuggester.suggestSimilarName("xyz123", availableNames);
        assertNull(noMatch);
        
        // Test exact match (should still return the name)
        String exact = NameSuggester.suggestSimilarName("print", availableNames);
        assertEquals("print", exact);
    }
    
    @Test
    @DisplayName("Test undefined variable with builtin suggestion")
    void testUndefinedVariableWithBuiltinSuggestion() {
        PythonExecutor executor = new PythonExecutor();
        
        try {
            // This should suggest 'len' for 'lem'
            executor.executeCode("lem");
            fail("Expected PythonExecutionException");
        } catch (PythonExecutionException e) {
            // The error should contain some form of suggestion
            String message = e.getMessage();
            assertTrue(message.contains("NameError") || message.contains("not defined"),
                      "Should be a NameError: " + message);
        }
        
        executor.close();
    }
}
