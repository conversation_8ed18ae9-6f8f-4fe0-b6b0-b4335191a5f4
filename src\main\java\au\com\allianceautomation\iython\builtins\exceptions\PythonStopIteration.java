package au.com.allianceautomation.iython.builtins.exceptions;

import java.util.List;

/**
 * Raised by built-in function next() and an iterator's __next__() method to signal 
 * that there are no further items produced by the iterator.
 * This corresponds to Python's StopIteration.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class PythonStopIteration extends PythonException {
    
    private static final long serialVersionUID = 1L;
    
    private Object value;
    
    /**
     * Constructs a new PythonStopIteration with no arguments.
     */
    public PythonStopIteration() {
        super();
        this.value = null;
    }
    
    /**
     * Constructs a new PythonStopIteration with the specified message.
     * 
     * @param message the detail message
     */
    public PythonStopIteration(String message) {
        super(message);
        this.value = null;
    }
    
    /**
     * Constructs a new PythonStopIteration with the specified value.
     * 
     * @param value the value returned by the iterator
     */
    public PythonStopIteration(Object value) {
        super(value != null ? String.valueOf(value) : "");
        this.value = value;
    }
    
    /**
     * Constructs a new PythonStopIteration with the specified arguments.
     * 
     * @param args the exception arguments
     */
    public PythonStopIteration(Object... args) {
        super(args);
        this.value = args.length > 0 ? args[0] : null;
    }
    
    /**
     * Constructs a new PythonStopIteration with the specified arguments.
     * 
     * @param args the exception arguments
     */
    public PythonStopIteration(List<Object> args) {
        super(args);
        this.value = args.size() > 0 ? args.get(0) : null;
    }
    
    /**
     * Get the value returned by the iterator.
     * 
     * @return the value
     */
    public Object getValue() {
        return value;
    }
    
    /**
     * Set the value returned by the iterator.
     * 
     * @param value the value
     */
    public void setValue(Object value) {
        this.value = value;
    }
    
    @Override
    public String getPythonClassName() {
        return "StopIteration";
    }
}
