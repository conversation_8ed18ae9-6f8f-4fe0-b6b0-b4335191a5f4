package au.com.allianceautomation.iython.builtins.functions;

import au.com.allianceautomation.iython.builtins.AbstractBuiltinFunction;
import au.com.allianceautomation.iython.builtins.BuiltinFunction;
import java.util.List;
import java.lang.reflect.Method;

/**
 * Python callable() builtin function.
 * 
 * Returns True if the object appears callable (i.e., has a __call__ method), False otherwise.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class CallableFunction extends AbstractBuiltinFunction {
    
    public CallableFunction() {
        super("callable", 1, 1, "callable(obj) -> bool");
    }
    
    @Override
    protected Object execute(List<Object> args) {
        Object obj = args.get(0);
        
        if (obj == null) {
            return false;
        }
        
        // Check if it's a builtin function
        if (obj instanceof BuiltinFunction) {
            return true;
        }
        
        // Check if it's a Java method or function
        if (obj instanceof Method) {
            return true;
        }
        
        // Check if the object has a __call__ method
        try {
            Method callMethod = obj.getClass().getMethod("__call__");
            return callMethod != null;
        } catch (NoSuchMethodException e) {
            // No __call__ method found
        }
        
        // Check if it's a class (classes are callable in Python)
        if (obj instanceof Class) {
            return true;
        }
        
        // For now, assume other objects are not callable
        // In a full implementation, we would check for __call__ attribute
        return false;
    }
}
