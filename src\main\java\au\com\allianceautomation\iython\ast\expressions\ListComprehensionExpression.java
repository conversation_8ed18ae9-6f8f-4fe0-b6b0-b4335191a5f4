package au.com.allianceautomation.iython.ast.expressions;

import au.com.allianceautomation.iython.ast.ASTVisitor;
import java.util.List;

/**
 * AST node representing list comprehensions.
 * Represents expressions like: [expr for var in iterable if condition]
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class ListComprehensionExpression extends Expression {
    private final Expression element;
    private final List<ComprehensionClause> generators;
    
    public ListComprehensionExpression(int line, int column, Expression element, List<ComprehensionClause> generators) {
        super(line, column);
        this.element = element;
        this.generators = generators;
    }
    
    public Expression getElement() { 
        return element; 
    }
    
    public List<ComprehensionClause> getGenerators() { 
        return generators; 
    }
    
    @Override
    public <T> T accept(ASTVisitor<T> visitor) {
        return visitor.visitListComprehension(this);
    }
    
    @Override
    public String toString() {
        return "ListComprehension(" + element + " for " + generators + ")";
    }
    
    /**
     * Represents a single comprehension clause: "for var in iterable if condition"
     */
    public static class ComprehensionClause {
        private final Expression target;
        private final Expression iter;
        private final List<Expression> ifs;
        
        public ComprehensionClause(Expression target, Expression iter, List<Expression> ifs) {
            this.target = target;
            this.iter = iter;
            this.ifs = ifs;
        }
        
        public Expression getTarget() { return target; }
        public Expression getIter() { return iter; }
        public List<Expression> getIfs() { return ifs; }
        
        @Override
        public String toString() {
            StringBuilder sb = new StringBuilder();
            sb.append(target).append(" in ").append(iter);
            for (Expression ifExpr : ifs) {
                sb.append(" if ").append(ifExpr);
            }
            return sb.toString();
        }
    }
}
