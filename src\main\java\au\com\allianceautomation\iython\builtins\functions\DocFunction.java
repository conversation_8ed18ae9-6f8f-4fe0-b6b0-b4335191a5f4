package au.com.allianceautomation.iython.builtins.functions;

import au.com.allianceautomation.iython.builtins.AbstractBuiltinFunction;
import java.util.List;

/**
 * Python __doc__ builtin attribute.
 * 
 * This represents the documentation string for the builtins module.
 * In CPython, this contains documentation about the built-in functions and types.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class DocFunction extends AbstractBuiltinFunction {
    
    public DocFunction() {
        super("__doc__", 0, 0, "__doc__ -> str");
    }
    
    @Override
    protected Object execute(List<Object> args) {
        // Return documentation string for the builtins module
        return "Built-in functions, exceptions, and other objects.\n\n" +
               "This module provides direct access to all 'built-in' identifiers of Python;\n" +
               "for example, builtins.open is the full name for the built-in function open().\n" +
               "See 'Built-in Functions' and 'Built-in Constants' in the Python Library\n" +
               "Reference for documentation.\n\n" +
               "This is the iython implementation of Python built-ins.";
    }
}
