package au.com.allianceautomation.iython.builtins.functions;

import au.com.allianceautomation.iython.builtins.AbstractBuiltinFunction;
import java.util.List;

/**
 * Python slice() builtin function.
 * 
 * Returns a slice object representing the set of indices specified by range(start, stop, step).
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class SliceFunction extends AbstractBuiltinFunction {
    
    public SliceFunction() {
        super("slice", 1, 3, "slice(stop) or slice(start, stop[, step]) -> slice");
    }
    
    @Override
    protected Object execute(List<Object> args) {
        if (args.size() == 1) {
            // slice(stop)
            Object stop = args.get(0);
            return new PythonSlice(null, stop, null);
        } else if (args.size() == 2) {
            // slice(start, stop)
            Object start = args.get(0);
            Object stop = args.get(1);
            return new PythonSlice(start, stop, null);
        } else if (args.size() == 3) {
            // slice(start, stop, step)
            Object start = args.get(0);
            Object stop = args.get(1);
            Object step = args.get(2);
            return new PythonSlice(start, stop, step);
        } else {
            throw new RuntimeException("TypeError: slice expected at most 3 arguments, got " + args.size());
        }
    }
    
    /**
     * Slice object for Python.
     */
    public static class PythonSlice {
        private final Object start;
        private final Object stop;
        private final Object step;
        
        public PythonSlice(Object start, Object stop, Object step) {
            this.start = start;
            this.stop = stop;
            this.step = step;
        }
        
        public Object getStart() {
            return start;
        }
        
        public Object getStop() {
            return stop;
        }
        
        public Object getStep() {
            return step;
        }
        
        /**
         * Convert slice indices to concrete start, stop, step values for a given length.
         */
        public SliceIndices indices(int length) {
            int startIdx, stopIdx, stepIdx;
            
            // Handle step
            if (step == null) {
                stepIdx = 1;
            } else if (step instanceof Integer) {
                stepIdx = (Integer) step;
                if (stepIdx == 0) {
                    throw new RuntimeException("ValueError: slice step cannot be zero");
                }
            } else {
                throw new RuntimeException("TypeError: slice indices must be integers or None");
            }
            
            // Handle start
            if (start == null) {
                startIdx = stepIdx < 0 ? length - 1 : 0;
            } else if (start instanceof Integer) {
                startIdx = (Integer) start;
                if (startIdx < 0) {
                    startIdx += length;
                }
                startIdx = Math.max(0, Math.min(startIdx, length - 1));
            } else {
                throw new RuntimeException("TypeError: slice indices must be integers or None");
            }
            
            // Handle stop
            if (stop == null) {
                stopIdx = stepIdx < 0 ? -1 : length;
            } else if (stop instanceof Integer) {
                stopIdx = (Integer) stop;
                if (stopIdx < 0) {
                    stopIdx += length;
                }
                stopIdx = Math.max(-1, Math.min(stopIdx, length));
            } else {
                throw new RuntimeException("TypeError: slice indices must be integers or None");
            }
            
            return new SliceIndices(startIdx, stopIdx, stepIdx);
        }
        
        @Override
        public String toString() {
            return "slice(" + start + ", " + stop + ", " + step + ")";
        }
        
        @Override
        public boolean equals(Object obj) {
            if (this == obj) return true;
            if (!(obj instanceof PythonSlice)) return false;
            PythonSlice other = (PythonSlice) obj;
            return java.util.Objects.equals(start, other.start) &&
                   java.util.Objects.equals(stop, other.stop) &&
                   java.util.Objects.equals(step, other.step);
        }
        
        @Override
        public int hashCode() {
            return java.util.Objects.hash(start, stop, step);
        }
    }
    
    /**
     * Concrete slice indices after processing None values and negative indices.
     */
    public static class SliceIndices {
        private final int start;
        private final int stop;
        private final int step;
        
        public SliceIndices(int start, int stop, int step) {
            this.start = start;
            this.stop = stop;
            this.step = step;
        }
        
        public int getStart() {
            return start;
        }
        
        public int getStop() {
            return stop;
        }
        
        public int getStep() {
            return step;
        }
        
        /**
         * Generate the sequence of indices for this slice.
         */
        public List<Integer> getIndices() {
            List<Integer> indices = new java.util.ArrayList<>();
            
            if (step > 0) {
                for (int i = start; i < stop; i += step) {
                    indices.add(i);
                }
            } else {
                for (int i = start; i > stop; i += step) {
                    indices.add(i);
                }
            }
            
            return indices;
        }
        
        @Override
        public String toString() {
            return "SliceIndices(start=" + start + ", stop=" + stop + ", step=" + step + ")";
        }
    }
}
