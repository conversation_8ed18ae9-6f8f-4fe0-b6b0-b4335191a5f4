package au.com.allianceautomation.iython.builtins.functions;

import au.com.allianceautomation.iython.builtins.AbstractBuiltinFunction;
import au.com.allianceautomation.iython.builtins.BuiltinFunction;
import java.util.List;

/**
 * Python classmethod() builtin function.
 * 
 * Returns a class method for function.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class ClassMethodFunction extends AbstractBuiltinFunction {
    
    public ClassMethodFunction() {
        super("classmethod", 1, 1, "classmethod(function) -> method");
    }
    
    @Override
    protected Object execute(List<Object> args) {
        Object function = args.get(0);
        
        if (function == null) {
            throw new RuntimeException("TypeError: classmethod expected 1 argument, got 0");
        }
        
        return new PythonClassMethod(function);
    }
    
    /**
     * Class method descriptor for Python.
     */
    public static class PythonClassMethod {
        private final Object function;
        
        public PythonClassMethod(Object function) {
            this.function = function;
        }
        
        public Object getFunction() {
            return function;
        }
        
        public Object get(Object instance, Object owner) {
            // For class methods, the first argument is always the class (owner)
            if (function instanceof BuiltinFunction) {
                BuiltinFunction func = (BuiltinFunction) function;
                return new BoundClassMethod(func, owner != null ? owner : instance.getClass());
            } else {
                // For now, assume other callables are not supported
                throw new RuntimeException("TypeError: classmethod function not callable");
            }
        }
        
        @Override
        public String toString() {
            return "<classmethod object at 0x" + Integer.toHexString(System.identityHashCode(this)) + ">";
        }
    }
    
    /**
     * Bound class method that automatically passes the class as first argument.
     */
    public static class BoundClassMethod {
        private final BuiltinFunction function;
        private final Object cls;
        
        public BoundClassMethod(BuiltinFunction function, Object cls) {
            this.function = function;
            this.cls = cls;
        }
        
        public Object call(List<Object> args) {
            // Prepend the class as the first argument
            List<Object> newArgs = new java.util.ArrayList<>();
            newArgs.add(cls);
            newArgs.addAll(args);
            return function.call(newArgs);
        }
        
        @Override
        public String toString() {
            return "<bound method " + function.getName() + " of " + cls + ">";
        }
    }
}
