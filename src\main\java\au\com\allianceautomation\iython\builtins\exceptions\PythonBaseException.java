package au.com.allianceautomation.iython.builtins.exceptions;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Base class for all Python built-in exceptions.
 * This corresponds to Python's BaseException.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class PythonBaseException extends RuntimeException {
    
    private static final long serialVersionUID = 1L;
    
    protected final List<Object> args;
    protected PythonBaseException context;
    protected PythonBaseException cause;
    protected boolean suppressContext = false;
    protected final List<String> notes = new ArrayList<>();
    
    /**
     * Constructs a new PythonBaseException with no arguments.
     */
    public PythonBaseException() {
        this.args = new ArrayList<>();
    }
    
    /**
     * Constructs a new PythonBaseException with the specified message.
     * 
     * @param message the detail message
     */
    public PythonBaseException(String message) {
        super(message);
        this.args = new ArrayList<>();
        this.args.add(message);
    }
    
    /**
     * Constructs a new PythonBaseException with the specified arguments.
     * 
     * @param args the exception arguments
     */
    public PythonBaseException(Object... args) {
        super(args.length > 0 ? String.valueOf(args[0]) : "");
        this.args = new ArrayList<>(Arrays.asList(args));
    }
    
    /**
     * Constructs a new PythonBaseException with the specified arguments.
     * 
     * @param args the exception arguments
     */
    public PythonBaseException(List<Object> args) {
        super(args.size() > 0 ? String.valueOf(args.get(0)) : "");
        this.args = new ArrayList<>(args);
    }
    
    /**
     * Get the exception arguments.
     * 
     * @return the tuple of arguments given to the exception constructor
     */
    public List<Object> getArgs() {
        return new ArrayList<>(args);
    }
    
    /**
     * Set the traceback for this exception and return the exception object.
     * 
     * @param tb the traceback object
     * @return this exception object
     */
    public PythonBaseException withTraceback(Object tb) {
        // For now, we don't implement full traceback support
        return this;
    }
    
    /**
     * Add a note to this exception.
     * 
     * @param note the note to add
     */
    public void addNote(String note) {
        if (note == null) {
            throw new PythonTypeError("note must be a string");
        }
        notes.add(note);
    }
    
    /**
     * Get the notes for this exception.
     * 
     * @return the list of notes
     */
    public List<String> getNotes() {
        return new ArrayList<>(notes);
    }
    
    /**
     * Get the exception context.
     * 
     * @return the exception context
     */
    public PythonBaseException getContext() {
        return context;
    }
    
    /**
     * Set the exception context.
     * 
     * @param context the exception context
     */
    public void setContext(PythonBaseException context) {
        this.context = context;
    }
    
    /**
     * Get the exception cause.
     * 
     * @return the exception cause
     */
    public PythonBaseException getCause() {
        return cause;
    }
    
    /**
     * Set the exception cause.
     * 
     * @param cause the exception cause
     */
    public void setCause(PythonBaseException cause) {
        this.cause = cause;
        if (cause != null) {
            this.suppressContext = true;
        }
    }
    
    /**
     * Check if context should be suppressed.
     * 
     * @return true if context should be suppressed
     */
    public boolean isSuppressContext() {
        return suppressContext;
    }
    
    /**
     * Set whether context should be suppressed.
     * 
     * @param suppressContext whether to suppress context
     */
    public void setSuppressContext(boolean suppressContext) {
        this.suppressContext = suppressContext;
    }
    
    /**
     * Get the Python class name for this exception.
     * 
     * @return the Python class name
     */
    public String getPythonClassName() {
        return "BaseException";
    }
    
    @Override
    public String toString() {
        if (args.isEmpty()) {
            return getPythonClassName();
        } else if (args.size() == 1) {
            return getPythonClassName() + ": " + args.get(0);
        } else {
            return getPythonClassName() + ": " + args.toString();
        }
    }
}
