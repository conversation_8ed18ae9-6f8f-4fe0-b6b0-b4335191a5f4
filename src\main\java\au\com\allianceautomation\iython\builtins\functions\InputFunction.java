package au.com.allianceautomation.iython.builtins.functions;

import au.com.allianceautomation.iython.builtins.AbstractBuiltinFunction;
import java.util.List;
import java.util.Scanner;

/**
 * Python input() builtin function.
 * 
 * Reads a line from input, converts it to a string (stripping a trailing newline), and returns that.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class InputFunction extends AbstractBuiltinFunction {
    
    private static final Scanner scanner = new Scanner(System.in);
    
    public InputFunction() {
        super("input", 0, 1, "input([prompt]) -> str");
    }
    
    @Override
    protected Object execute(List<Object> args) {
        // Print prompt if provided
        if (!args.isEmpty()) {
            Object prompt = args.get(0);
            System.out.print(pythonStr(prompt));
        }
        
        // Read line from input
        try {
            return scanner.nextLine();
        } catch (Exception e) {
            throw new RuntimeException("EOFError: EOF when reading a line");
        }
    }
}
