package au.com.allianceautomation.iython.builtins.functions;

import au.com.allianceautomation.iython.builtins.AbstractBuiltinFunction;
import au.com.allianceautomation.iython.builtins.functions.ObjectFunction.PythonObject;
import java.util.List;
import java.lang.reflect.Field;

/**
 * Python delattr() builtin function.
 * 
 * Deletes the named attribute from the given object.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class DelAttrFunction extends AbstractBuiltinFunction {
    
    public DelAttrFunction() {
        super("delattr", 2, 2, "delattr(obj, name) -> None");
    }
    
    @Override
    protected Object execute(List<Object> args) {
        Object obj = args.get(0);
        String name = pythonStr(args.get(1));
        
        if (obj == null) {
            throw new RuntimeException("TypeError: delattr expected 2 arguments, got 1");
        }
        
        // Handle PythonObject instances
        if (obj instanceof PythonObject) {
            PythonObject pythonObj = (PythonObject) obj;
            if (pythonObj.hasAttribute(name)) {
                pythonObj.deleteAttribute(name);
                return null; // delattr returns None
            } else {
                throw new RuntimeException("AttributeError: '" + obj.getClass().getSimpleName() + 
                                         "' object has no attribute '" + name + "'");
            }
        }
        
        // Try to delete using reflection for other objects
        try {
            Class<?> clazz = obj.getClass();
            Field field = clazz.getDeclaredField(name);
            field.setAccessible(true);
            
            // Check if the field can be set to null (for reference types)
            if (!field.getType().isPrimitive()) {
                field.set(obj, null);
                return null;
            } else {
                throw new RuntimeException("AttributeError: cannot delete primitive field '" + name + "'");
            }
        } catch (NoSuchFieldException e) {
            throw new RuntimeException("AttributeError: '" + obj.getClass().getSimpleName() + 
                                     "' object has no attribute '" + name + "'");
        } catch (IllegalAccessException e) {
            throw new RuntimeException("AttributeError: cannot delete attribute '" + name + "'");
        } catch (SecurityException e) {
            throw new RuntimeException("AttributeError: cannot access attribute '" + name + "'");
        }
    }
}
