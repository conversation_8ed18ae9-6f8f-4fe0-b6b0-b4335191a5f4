package au.com.allianceautomation.iython.builtins;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test class for Python internal functions (dunder methods) in iython.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
class InternalFunctionsTest {
    
    @Test
    @DisplayName("Test all internal functions are registered")
    void testInternalFunctionsRegistered() {
        // List of all internal functions from CPython's dir(__builtins__)
        List<String> internalFunctions = Arrays.asList(
            "__build_class__",
            "__debug__", 
            "__doc__",
            "__import__",
            "__loader__",
            "__name__",
            "__package__",
            "__spec__"
        );
        
        // Check that all internal functions are registered
        for (String funcName : internalFunctions) {
            assertTrue(BuiltinRegistry.isBuiltin(funcName), 
                "Internal function " + funcName + " should be registered");
            assertNotNull(BuiltinRegistry.getBuiltin(funcName),
                "Internal function " + funcName + " should not be null");
        }
    }
    
    @Test
    @DisplayName("Test __build_class__ function")
    void testBuildClassFunction() {
        BuiltinFunction buildClass = BuiltinRegistry.getBuiltin("__build_class__");
        assertNotNull(buildClass);
        
        // Test with minimal arguments
        List<Object> args = Arrays.asList(
            new Object(), // function placeholder
            "TestClass"   // class name
        );
        
        Object result = buildClass.call(args);
        assertNotNull(result);
        assertTrue(result.toString().contains("TestClass"));
    }
    
    @Test
    @DisplayName("Test __debug__ function")
    void testDebugFunction() {
        BuiltinFunction debug = BuiltinRegistry.getBuiltin("__debug__");
        assertNotNull(debug);
        
        Object result = debug.call(new ArrayList<>());
        assertNotNull(result);
        assertTrue(result instanceof Boolean);
        assertTrue((Boolean) result); // Should be true in iython
    }
    
    @Test
    @DisplayName("Test __doc__ function")
    void testDocFunction() {
        BuiltinFunction doc = BuiltinRegistry.getBuiltin("__doc__");
        assertNotNull(doc);
        
        Object result = doc.call(new ArrayList<>());
        assertNotNull(result);
        assertTrue(result instanceof String);
        String docString = (String) result;
        assertTrue(docString.contains("Built-in functions"));
        assertTrue(docString.contains("iython"));
    }
    
    @Test
    @DisplayName("Test __import__ function")
    void testImportFunction() {
        BuiltinFunction importFunc = BuiltinRegistry.getBuiltin("__import__");
        assertNotNull(importFunc);
        
        List<Object> args = Arrays.asList("test_module");
        Object result = importFunc.call(args);
        assertNotNull(result);
        assertTrue(result.toString().contains("test_module"));
    }
    
    @Test
    @DisplayName("Test __loader__ function")
    void testLoaderFunction() {
        BuiltinFunction loader = BuiltinRegistry.getBuiltin("__loader__");
        assertNotNull(loader);
        
        Object result = loader.call(new ArrayList<>());
        assertNotNull(result);
        assertTrue(result.toString().contains("BuiltinImporter"));
    }
    
    @Test
    @DisplayName("Test __name__ function")
    void testNameFunction() {
        BuiltinFunction name = BuiltinRegistry.getBuiltin("__name__");
        assertNotNull(name);
        
        Object result = name.call(new ArrayList<>());
        assertNotNull(result);
        assertEquals("builtins", result);
    }
    
    @Test
    @DisplayName("Test __package__ function")
    void testPackageFunction() {
        BuiltinFunction packageFunc = BuiltinRegistry.getBuiltin("__package__");
        assertNotNull(packageFunc);
        
        Object result = packageFunc.call(new ArrayList<>());
        assertNull(result); // Should be None/null for builtins module
    }
    
    @Test
    @DisplayName("Test __spec__ function")
    void testSpecFunction() {
        BuiltinFunction spec = BuiltinRegistry.getBuiltin("__spec__");
        assertNotNull(spec);
        
        Object result = spec.call(new ArrayList<>());
        assertNotNull(result);
        assertTrue(result.toString().contains("ModuleSpec"));
        assertTrue(result.toString().contains("builtins"));
    }
    
    @Test
    @DisplayName("Test builtin count includes internal functions")
    void testBuiltinCountIncludesInternalFunctions() {
        // Should have at least the original functions plus 8 internal functions
        assertTrue(BuiltinRegistry.getBuiltinCount() >= 38, 
            "Should have at least 38 builtin functions including internal ones");
    }
}
