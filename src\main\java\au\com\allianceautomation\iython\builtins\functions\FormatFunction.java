package au.com.allianceautomation.iython.builtins.functions;

import au.com.allianceautomation.iython.builtins.AbstractBuiltinFunction;
import java.util.List;

/**
 * Python format() builtin function.
 * 
 * Converts a value to a "formatted" representation, as controlled by format_spec.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class FormatFunction extends AbstractBuiltinFunction {
    
    public FormatFunction() {
        super("format", 1, 2, "format(value[, format_spec]) -> str");
    }
    
    @Override
    protected Object execute(List<Object> args) {
        Object value = args.get(0);
        String formatSpec = args.size() > 1 ? pythonStr(args.get(1)) : "";
        
        if (value == null) {
            return "None";
        }
        
        // If no format spec, just convert to string
        if (formatSpec.isEmpty()) {
            return pythonStr(value);
        }
        
        // Handle basic format specifications
        try {
            if (value instanceof Number) {
                return formatNumber((Number) value, formatSpec);
            } else if (value instanceof String) {
                return formatString((String) value, formatSpec);
            } else {
                // For other types, just return string representation
                return pythonStr(value);
            }
        } catch (Exception e) {
            throw new RuntimeException("Invalid format specifier '" + formatSpec + "' for object of type '" + 
                                     value.getClass().getSimpleName() + "'");
        }
    }
    
    private String formatNumber(Number number, String formatSpec) {
        // Handle basic numeric format specifications
        if (formatSpec.equals("d")) {
            return String.valueOf(number.longValue());
        } else if (formatSpec.equals("f")) {
            return String.valueOf(number.doubleValue());
        } else if (formatSpec.equals("x")) {
            return Long.toHexString(number.longValue());
        } else if (formatSpec.equals("X")) {
            return Long.toHexString(number.longValue()).toUpperCase();
        } else if (formatSpec.equals("o")) {
            return Long.toOctalString(number.longValue());
        } else if (formatSpec.equals("b")) {
            return Long.toBinaryString(number.longValue());
        } else if (formatSpec.matches("\\d+")) {
            // Width specification
            int width = Integer.parseInt(formatSpec);
            String str = number.toString();
            return String.format("%" + width + "s", str);
        } else if (formatSpec.matches("\\.\\d+f")) {
            // Precision specification for floats
            int precision = Integer.parseInt(formatSpec.substring(1, formatSpec.length() - 1));
            return String.format("%." + precision + "f", number.doubleValue());
        }
        
        // Default: just return string representation
        return number.toString();
    }
    
    private String formatString(String str, String formatSpec) {
        // Handle basic string format specifications
        if (formatSpec.matches("\\d+")) {
            // Width specification
            int width = Integer.parseInt(formatSpec);
            return String.format("%" + width + "s", str);
        } else if (formatSpec.matches("<\\d+")) {
            // Left align with width
            int width = Integer.parseInt(formatSpec.substring(1));
            return String.format("%-" + width + "s", str);
        } else if (formatSpec.matches(">\\d+")) {
            // Right align with width
            int width = Integer.parseInt(formatSpec.substring(1));
            return String.format("%" + width + "s", str);
        } else if (formatSpec.matches("\\^\\d+")) {
            // Center align with width
            int width = Integer.parseInt(formatSpec.substring(1));
            int padding = Math.max(0, width - str.length());
            int leftPad = padding / 2;
            int rightPad = padding - leftPad;
            return " ".repeat(leftPad) + str + " ".repeat(rightPad);
        }
        
        // Default: just return the string
        return str;
    }
}
