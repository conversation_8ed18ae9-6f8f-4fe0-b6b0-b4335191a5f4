package au.com.allianceautomation.iython.builtins;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

import au.com.allianceautomation.iython.builtins.functions.AbsFunction;
import au.com.allianceautomation.iython.builtins.functions.AllFunction;
import au.com.allianceautomation.iython.builtins.functions.AnyFunction;
import au.com.allianceautomation.iython.builtins.functions.AsciiFunction;
import au.com.allianceautomation.iython.builtins.functions.BinFunction;
import au.com.allianceautomation.iython.builtins.functions.BoolFunction;
import au.com.allianceautomation.iython.builtins.functions.BuildClassFunction;
import au.com.allianceautomation.iython.builtins.functions.ByteArrayFunction;
import au.com.allianceautomation.iython.builtins.functions.BytesFunction;
import au.com.allianceautomation.iython.builtins.functions.CallableFunction;
import au.com.allianceautomation.iython.builtins.functions.ChrFunction;
import au.com.allianceautomation.iython.builtins.functions.ClassMethodFunction;
import au.com.allianceautomation.iython.builtins.functions.CompileFunction;
import au.com.allianceautomation.iython.builtins.functions.ComplexFunction;
import au.com.allianceautomation.iython.builtins.functions.DebugFunction;
import au.com.allianceautomation.iython.builtins.functions.DelAttrFunction;
import au.com.allianceautomation.iython.builtins.functions.DictFunction;
import au.com.allianceautomation.iython.builtins.functions.DirFunction;
import au.com.allianceautomation.iython.builtins.functions.DivModFunction;
import au.com.allianceautomation.iython.builtins.functions.DocFunction;
import au.com.allianceautomation.iython.builtins.functions.EnumerateFunction;
import au.com.allianceautomation.iython.builtins.functions.EvalFunction;
import au.com.allianceautomation.iython.builtins.functions.ExecFunction;
import au.com.allianceautomation.iython.builtins.functions.FilterFunction;
import au.com.allianceautomation.iython.builtins.functions.FloatFunction;
import au.com.allianceautomation.iython.builtins.functions.FormatFunction;
import au.com.allianceautomation.iython.builtins.functions.FrozenSetFunction;
import au.com.allianceautomation.iython.builtins.functions.GetAttrFunction;
import au.com.allianceautomation.iython.builtins.functions.GlobalsFunction;
import au.com.allianceautomation.iython.builtins.functions.HasAttrFunction;
import au.com.allianceautomation.iython.builtins.functions.HashFunction;
import au.com.allianceautomation.iython.builtins.functions.HelpFunction;
import au.com.allianceautomation.iython.builtins.functions.HexFunction;
import au.com.allianceautomation.iython.builtins.functions.IdFunction;
import au.com.allianceautomation.iython.builtins.functions.ImportFunction;
import au.com.allianceautomation.iython.builtins.functions.InputFunction;
import au.com.allianceautomation.iython.builtins.functions.IntFunction;
import au.com.allianceautomation.iython.builtins.functions.IsInstanceFunction;
import au.com.allianceautomation.iython.builtins.functions.IterFunction;
import au.com.allianceautomation.iython.builtins.functions.LenFunction;
import au.com.allianceautomation.iython.builtins.functions.ListFunction;
import au.com.allianceautomation.iython.builtins.functions.LoaderFunction;
import au.com.allianceautomation.iython.builtins.functions.LocalsFunction;
import au.com.allianceautomation.iython.builtins.functions.MapFunction;
import au.com.allianceautomation.iython.builtins.functions.MaxFunction;
import au.com.allianceautomation.iython.builtins.functions.MemoryViewFunction;
import au.com.allianceautomation.iython.builtins.functions.MinFunction;
import au.com.allianceautomation.iython.builtins.functions.NameFunction;
import au.com.allianceautomation.iython.builtins.functions.NextFunction;
import au.com.allianceautomation.iython.builtins.functions.ObjectFunction;
import au.com.allianceautomation.iython.builtins.functions.OctFunction;
import au.com.allianceautomation.iython.builtins.functions.OpenFunction;
import au.com.allianceautomation.iython.builtins.functions.OrdFunction;
import au.com.allianceautomation.iython.builtins.functions.PackageFunction;
import au.com.allianceautomation.iython.builtins.functions.PowFunction;
import au.com.allianceautomation.iython.builtins.functions.PrintFunction;
import au.com.allianceautomation.iython.builtins.functions.PropertyFunction;
import au.com.allianceautomation.iython.builtins.functions.RangeFunction;
import au.com.allianceautomation.iython.builtins.functions.ReprFunction;
import au.com.allianceautomation.iython.builtins.functions.ReversedFunction;
import au.com.allianceautomation.iython.builtins.functions.RoundFunction;
import au.com.allianceautomation.iython.builtins.functions.SetAttrFunction;
import au.com.allianceautomation.iython.builtins.functions.SetFunction;
import au.com.allianceautomation.iython.builtins.functions.SliceFunction;
import au.com.allianceautomation.iython.builtins.functions.SortedFunction;
import au.com.allianceautomation.iython.builtins.functions.SpecFunction;
import au.com.allianceautomation.iython.builtins.functions.StaticMethodFunction;
import au.com.allianceautomation.iython.builtins.functions.StrFunction;
import au.com.allianceautomation.iython.builtins.functions.SumFunction;
import au.com.allianceautomation.iython.builtins.functions.SuperFunction;
import au.com.allianceautomation.iython.builtins.functions.TupleFunction;
import au.com.allianceautomation.iython.builtins.functions.TypeFunction;
import au.com.allianceautomation.iython.builtins.functions.VarsFunction;
import au.com.allianceautomation.iython.builtins.functions.ZipFunction;

/**
 * Registry for all Python builtin functions and exceptions.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class BuiltinRegistry {
    
    private static final Map<String, BuiltinFunction> builtins = new HashMap<>();
    
    static {
        // Initialize all builtin functions

        // Type conversion functions
        registerBuiltin(new IntFunction());
        registerBuiltin(new FloatFunction());
        registerBuiltin(new BoolFunction());
        registerBuiltin(new StrFunction());
        registerBuiltin(new ListFunction());
        registerBuiltin(new DictFunction());
        registerBuiltin(new TupleFunction());
        registerBuiltin(new SetFunction());
        registerBuiltin(new ComplexFunction());
        registerBuiltin(new BytesFunction());
        registerBuiltin(new ByteArrayFunction());
        registerBuiltin(new FrozenSetFunction());
        registerBuiltin(new MemoryViewFunction());
        registerBuiltin(new ObjectFunction());

        // I/O functions
        registerBuiltin(new PrintFunction());
        registerBuiltin(new InputFunction());
        registerBuiltin(new OpenFunction());

        // Math functions
        registerBuiltin(new AbsFunction());
        registerBuiltin(new MinFunction());
        registerBuiltin(new MaxFunction());
        registerBuiltin(new SumFunction());
        registerBuiltin(new RoundFunction());
        registerBuiltin(new PowFunction());
        registerBuiltin(new DivModFunction());

        // Sequence functions
        registerBuiltin(new LenFunction());
        registerBuiltin(new RangeFunction());
        registerBuiltin(new EnumerateFunction());
        registerBuiltin(new ZipFunction());
        registerBuiltin(new SortedFunction());
        registerBuiltin(new ReversedFunction());

        // String/Character functions
        registerBuiltin(new OrdFunction());
        registerBuiltin(new ChrFunction());
        registerBuiltin(new ReprFunction());

        // Number base conversion functions
        registerBuiltin(new HexFunction());
        registerBuiltin(new OctFunction());
        registerBuiltin(new BinFunction());

        // Object introspection functions
        registerBuiltin(new TypeFunction());
        registerBuiltin(new IsInstanceFunction());
        registerBuiltin(new HasAttrFunction());
        registerBuiltin(new GetAttrFunction());
        registerBuiltin(new SetAttrFunction());
        registerBuiltin(new DirFunction());
        registerBuiltin(new CallableFunction());
        registerBuiltin(new IdFunction());
        registerBuiltin(new HashFunction());
        registerBuiltin(new DelAttrFunction());

        // Iterator functions
        registerBuiltin(new AllFunction());
        registerBuiltin(new AnyFunction());
        registerBuiltin(new FilterFunction());
        registerBuiltin(new MapFunction());
        registerBuiltin(new IterFunction());
        registerBuiltin(new NextFunction());

        // String formatting functions
        registerBuiltin(new AsciiFunction());
        registerBuiltin(new FormatFunction());

        // Namespace functions
        registerBuiltin(new GlobalsFunction());
        registerBuiltin(new LocalsFunction());
        registerBuiltin(new VarsFunction());

        // Execution functions
        registerBuiltin(new EvalFunction());
        registerBuiltin(new ExecFunction());
        registerBuiltin(new CompileFunction());

        // Class decorators and descriptors
        registerBuiltin(new PropertyFunction());
        registerBuiltin(new ClassMethodFunction());
        registerBuiltin(new StaticMethodFunction());

        // Advanced types
        registerBuiltin(new SliceFunction());
        registerBuiltin(new SuperFunction());

        // Help system
        registerBuiltin(new HelpFunction());

        // Internal Python functions (dunder methods)
        registerBuiltin(new BuildClassFunction());
        registerBuiltin(new DebugFunction());
        registerBuiltin(new DocFunction());
        registerBuiltin(new ImportFunction());
        registerBuiltin(new LoaderFunction());
        registerBuiltin(new NameFunction());
        registerBuiltin(new PackageFunction());
        registerBuiltin(new SpecFunction());
    }
    
    /**
     * Register a builtin function.
     * 
     * @param function The function to register
     */
    private static void registerBuiltin(BuiltinFunction function) {
        builtins.put(function.getName(), function);
    }
    
    /**
     * Get a builtin function by name.
     * 
     * @param name The name of the function
     * @return The builtin function, or null if not found
     */
    public static BuiltinFunction getBuiltin(String name) {
        return builtins.get(name);
    }
    
    /**
     * Check if a name is a builtin function.
     * 
     * @param name The name to check
     * @return true if it's a builtin function
     */
    public static boolean isBuiltin(String name) {
        return builtins.containsKey(name);
    }
    
    /**
     * Get all builtin function names.
     * 
     * @return A set of all builtin function names
     */
    public static Set<String> getBuiltinNames() {
        return builtins.keySet();
    }
    
    /**
     * Get the total number of registered builtin functions.
     * 
     * @return The number of builtin functions
     */
    public static int getBuiltinCount() {
        return builtins.size();
    }
}
