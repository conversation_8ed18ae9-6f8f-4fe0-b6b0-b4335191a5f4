package au.com.allianceautomation.iython.builtins.exceptions;

import java.util.List;

/**
 * Base class for all built-in, non-system-exiting exceptions.
 * This corresponds to Python's Exception class.
 * All user-defined exceptions should also be derived from this class.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class PythonException extends PythonBaseException {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * Constructs a new PythonException with no arguments.
     */
    public PythonException() {
        super();
    }
    
    /**
     * Constructs a new PythonException with the specified message.
     * 
     * @param message the detail message
     */
    public PythonException(String message) {
        super(message);
    }
    
    /**
     * Constructs a new PythonException with the specified arguments.
     * 
     * @param args the exception arguments
     */
    public PythonException(Object... args) {
        super(args);
    }
    
    /**
     * Constructs a new PythonException with the specified arguments.
     * 
     * @param args the exception arguments
     */
    public PythonException(List<Object> args) {
        super(args);
    }
    
    @Override
    public String getPythonClassName() {
        return "Exception";
    }
}
