package au.com.allianceautomation.iython.builtins.exceptions.lookup;

import java.util.List;

/**
 * Raised when a sequence subscript is out of range.
 * This corresponds to Python's IndexError.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class PythonIndexError extends PythonLookupError {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * Constructs a new PythonIndexError with no arguments.
     */
    public PythonIndexError() {
        super();
    }
    
    /**
     * Constructs a new PythonIndexError with the specified message.
     * 
     * @param message the detail message
     */
    public PythonIndexError(String message) {
        super(message);
    }
    
    /**
     * Constructs a new PythonIndexError with the specified arguments.
     * 
     * @param args the exception arguments
     */
    public PythonIndexError(Object... args) {
        super(args);
    }
    
    /**
     * Constructs a new PythonIndexError with the specified arguments.
     * 
     * @param args the exception arguments
     */
    public PythonIndexError(List<Object> args) {
        super(args);
    }
    
    @Override
    public String getPythonClassName() {
        return "IndexError";
    }
}
