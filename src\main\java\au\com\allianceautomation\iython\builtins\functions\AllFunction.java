package au.com.allianceautomation.iython.builtins.functions;

import java.util.List;

import au.com.allianceautomation.iython.builtins.AbstractBuiltinFunction;

/**
 * Python all() builtin function.
 * 
 * Returns True if all elements of the iterable are true (or if the iterable is empty).
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class AllFunction extends AbstractBuiltinFunction {
    
    public AllFunction() {
        super("all", 1, 1, "all(iterable) -> bool");
    }
    
    @Override
    protected Object execute(List<Object> args) {
        Object iterable = args.get(0);
        
        if (iterable instanceof List) {
            List<?> list = (List<?>) iterable;
            for (Object item : list) {
                if (!isTruthy(item)) {
                    return false;
                }
            }
            return true;
        } else if (iterable instanceof String) {
            String str = (String) iterable;
            for (char c : str.toCharArray()) {
                if (!isTruthy(String.valueOf(c))) {
                    return false;
                }
            }
            return true;
        } else if (iterable.getClass().isArray()) {
            Object[] array = (Object[]) iterable;
            for (Object item : array) {
                if (!isTruthy(item)) {
                    return false;
                }
            }
            return true;
        } else {
            throw new RuntimeException("'" + iterable.getClass().getSimpleName() + "' object is not iterable");
        }
    }
}
