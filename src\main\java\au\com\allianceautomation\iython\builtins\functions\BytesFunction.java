package au.com.allianceautomation.iython.builtins.functions;

import au.com.allianceautomation.iython.builtins.AbstractBuiltinFunction;
import java.util.List;
import java.util.Arrays;
import java.nio.charset.StandardCharsets;

/**
 * Python bytes() builtin function.
 * 
 * Returns an immutable bytes object constructed from:
 * - bytes() -> empty bytes object
 * - bytes(int) -> zero-filled bytes object of given length
 * - bytes(iterable_of_ints) -> bytes object from sequence of integers 0-255
 * - bytes(string, encoding) -> bytes object from string using encoding
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class BytesFunction extends AbstractBuiltinFunction {
    
    public BytesFunction() {
        super("bytes", 0, 3, "bytes([source[, encoding[, errors]]]) -> bytes");
    }
    
    @Override
    protected Object execute(List<Object> args) {
        if (args.isEmpty()) {
            // bytes() -> empty bytes
            return new PythonBytes(new byte[0]);
        }
        
        Object source = args.get(0);
        
        if (source instanceof Integer) {
            // bytes(int) -> zero-filled bytes of given length
            int length = (Integer) source;
            if (length < 0) {
                throw new RuntimeException("ValueError: negative count");
            }
            return new PythonBytes(new byte[length]);
        } else if (source instanceof String) {
            // bytes(string, encoding)
            String str = (String) source;
            String encoding = args.size() > 1 ? pythonStr(args.get(1)) : "utf-8";
            
            try {
                byte[] bytes;
                switch (encoding.toLowerCase()) {
                    case "utf-8":
                    case "utf8":
                        bytes = str.getBytes(StandardCharsets.UTF_8);
                        break;
                    case "ascii":
                        bytes = str.getBytes(StandardCharsets.US_ASCII);
                        break;
                    case "latin-1":
                    case "iso-8859-1":
                        bytes = str.getBytes(StandardCharsets.ISO_8859_1);
                        break;
                    default:
                        throw new RuntimeException("LookupError: unknown encoding: " + encoding);
                }
                return new PythonBytes(bytes);
            } catch (Exception e) {
                throw new RuntimeException("UnicodeEncodeError: " + e.getMessage());
            }
        } else if (source instanceof List) {
            // bytes(iterable_of_ints)
            List<?> list = (List<?>) source;
            byte[] bytes = new byte[list.size()];
            for (int i = 0; i < list.size(); i++) {
                Object item = list.get(i);
                if (!(item instanceof Integer)) {
                    throw new RuntimeException("TypeError: an integer is required");
                }
                int value = (Integer) item;
                if (value < 0 || value > 255) {
                    throw new RuntimeException("ValueError: bytes must be in range(0, 256)");
                }
                bytes[i] = (byte) value;
            }
            return new PythonBytes(bytes);
        } else if (source.getClass().isArray()) {
            // Handle arrays
            Object[] array = (Object[]) source;
            byte[] bytes = new byte[array.length];
            for (int i = 0; i < array.length; i++) {
                if (!(array[i] instanceof Integer)) {
                    throw new RuntimeException("TypeError: an integer is required");
                }
                int value = (Integer) array[i];
                if (value < 0 || value > 255) {
                    throw new RuntimeException("ValueError: bytes must be in range(0, 256)");
                }
                bytes[i] = (byte) value;
            }
            return new PythonBytes(bytes);
        } else {
            throw new RuntimeException("TypeError: cannot convert '" + 
                                     source.getClass().getSimpleName() + "' object to bytes");
        }
    }
    
    /**
     * Immutable bytes object for Python.
     */
    public static class PythonBytes {
        private final byte[] data;
        
        public PythonBytes(byte[] data) {
            this.data = data.clone();
        }
        
        public byte[] getData() {
            return data.clone();
        }
        
        public int length() {
            return data.length;
        }
        
        public byte get(int index) {
            if (index < 0 || index >= data.length) {
                throw new RuntimeException("IndexError: index out of range");
            }
            return data[index];
        }
        
        @Override
        public String toString() {
            StringBuilder sb = new StringBuilder("b'");
            for (byte b : data) {
                int value = b & 0xFF;
                if (value >= 32 && value <= 126 && value != 39 && value != 92) {
                    // Printable ASCII (except single quote and backslash)
                    sb.append((char) value);
                } else {
                    sb.append(String.format("\\x%02x", value));
                }
            }
            sb.append("'");
            return sb.toString();
        }
        
        @Override
        public boolean equals(Object obj) {
            if (this == obj) return true;
            if (!(obj instanceof PythonBytes)) return false;
            PythonBytes other = (PythonBytes) obj;
            return Arrays.equals(data, other.data);
        }
        
        @Override
        public int hashCode() {
            return Arrays.hashCode(data);
        }
    }
}
