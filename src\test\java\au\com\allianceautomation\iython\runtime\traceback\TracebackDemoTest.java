package au.com.allianceautomation.iython.runtime.traceback;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;

import au.com.allianceautomation.iython.PythonExecutor;
import au.com.allianceautomation.iython.PythonExecutionException;

/**
 * Demo test to show enhanced traceback functionality.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
class TracebackDemoTest {
    
    @Test
    @DisplayName("Demo: Enhanced traceback with name suggestion")
    void demoEnhancedTraceback() {
        System.out.println("\n=== iython Enhanced Traceback Demo ===\n");
        
        PythonExecutor executor = new PythonExecutor();
        
        // Test 1: NameError with suggestion
        System.out.println("1. Testing NameError with 'Did you mean?' suggestion:");
        System.out.println("   Code: cars");
        try {
            executor.executeCode("cars");
        } catch (PythonExecutionException e) {
            System.out.println("   Result:");
            System.out.println("   " + e.getMessage().replace("\n", "\n   "));
        }
        
        System.out.println();
        
        // Test 2: Another NameError with suggestion
        System.out.println("2. Testing NameError with builtin suggestion:");
        System.out.println("   Code: lem");
        try {
            executor.executeCode("lem");
        } catch (PythonExecutionException e) {
            System.out.println("   Result:");
            System.out.println("   " + e.getMessage().replace("\n", "\n   "));
        }
        
        System.out.println();
        
        // Test 3: NameError with no good suggestion
        System.out.println("3. Testing NameError with no good suggestion:");
        System.out.println("   Code: xyz123");
        try {
            executor.executeCode("xyz123");
        } catch (PythonExecutionException e) {
            System.out.println("   Result:");
            System.out.println("   " + e.getMessage().replace("\n", "\n   "));
        }
        
        System.out.println();
        
        // Test 4: Valid code (should work)
        System.out.println("4. Testing valid code:");
        System.out.println("   Code: print('Hello, World!')");
        try {
            String result = executor.executeCode("print('Hello, World!')");
            System.out.println("   Result:");
            System.out.println("   " + result.trim());
        } catch (PythonExecutionException e) {
            System.out.println("   Unexpected error: " + e.getMessage());
        }
        
        System.out.println("\n=== Demo Complete ===\n");
        
        executor.close();
    }
}
