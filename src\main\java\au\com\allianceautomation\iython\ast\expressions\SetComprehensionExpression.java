package au.com.allianceautomation.iython.ast.expressions;

import au.com.allianceautomation.iython.ast.ASTVisitor;
import java.util.List;

/**
 * AST node representing set comprehensions.
 * Represents expressions like: {expr for var in iterable if condition}
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class SetComprehensionExpression extends Expression {
    private final Expression element;
    private final List<ListComprehensionExpression.ComprehensionClause> generators;
    
    public SetComprehensionExpression(int line, int column, Expression element, 
                                    List<ListComprehensionExpression.ComprehensionClause> generators) {
        super(line, column);
        this.element = element;
        this.generators = generators;
    }
    
    public Expression getElement() { 
        return element; 
    }
    
    public List<ListComprehensionExpression.ComprehensionClause> getGenerators() { 
        return generators; 
    }
    
    @Override
    public <T> T accept(ASTVisitor<T> visitor) {
        return visitor.visitSetComprehension(this);
    }
    
    @Override
    public String toString() {
        return "SetComprehension(" + element + " for " + generators + ")";
    }
}
