package au.com.allianceautomation.iython.builtins;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Integration test for dir(__builtins__) functionality with internal functions.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
class DirBuiltinsIntegrationTest {
    
    @Test
    @DisplayName("Test dir(__builtins__) includes internal functions")
    void testDirBuiltinsIncludesInternalFunctions() {
        // Get the BuiltinsModule instance
        BuiltinsModule builtinsModule = BuiltinsModule.getInstance();
        
        // Get all builtin names (this is what dir(__builtins__) returns)
        List<String> builtinNames = builtinsModule.getBuiltinNames();
        
        // List of all internal functions from CPython's dir(__builtins__)
        List<String> internalFunctions = Arrays.asList(
            "__build_class__",
            "__debug__", 
            "__doc__",
            "__import__",
            "__loader__",
            "__name__",
            "__package__",
            "__spec__"
        );
        
        // Check that all internal functions are included in dir(__builtins__)
        for (String funcName : internalFunctions) {
            assertTrue(builtinNames.contains(funcName), 
                "dir(__builtins__) should include internal function: " + funcName);
        }
        
        // Verify the list is sorted (as it should be)
        List<String> sortedNames = builtinNames.stream().sorted().toList();
        assertEquals(sortedNames, builtinNames, "Builtin names should be sorted");
    }
    
    @Test
    @DisplayName("Test DirFunction returns internal functions when called with __builtins__")
    void testDirFunctionWithBuiltins() {
        // Get the dir function
        BuiltinFunction dirFunction = BuiltinRegistry.getBuiltin("dir");
        assertNotNull(dirFunction);
        
        // Get the __builtins__ module
        BuiltinsModule builtinsModule = BuiltinsModule.getInstance();
        
        // Call dir(__builtins__)
        List<Object> args = Arrays.asList(builtinsModule);
        Object result = dirFunction.call(args);
        
        assertNotNull(result);
        assertTrue(result instanceof List);
        
        @SuppressWarnings("unchecked")
        List<String> dirResult = (List<String>) result;
        
        // Check that internal functions are included
        List<String> internalFunctions = Arrays.asList(
            "__build_class__",
            "__debug__", 
            "__doc__",
            "__import__",
            "__loader__",
            "__name__",
            "__package__",
            "__spec__"
        );
        
        for (String funcName : internalFunctions) {
            assertTrue(dirResult.contains(funcName), 
                "dir(__builtins__) should include internal function: " + funcName);
        }
    }
    
    @Test
    @DisplayName("Test internal functions are accessible via BuiltinsModule")
    void testInternalFunctionsAccessibleViaBuiltinsModule() {
        BuiltinsModule builtinsModule = BuiltinsModule.getInstance();
        
        // List of all internal functions
        List<String> internalFunctions = Arrays.asList(
            "__build_class__",
            "__debug__", 
            "__doc__",
            "__import__",
            "__loader__",
            "__name__",
            "__package__",
            "__spec__"
        );
        
        // Check that all internal functions are accessible
        for (String funcName : internalFunctions) {
            assertTrue(builtinsModule.hasBuiltin(funcName), 
                "BuiltinsModule should have internal function: " + funcName);
            assertNotNull(builtinsModule.getBuiltin(funcName),
                "BuiltinsModule should return non-null for internal function: " + funcName);
        }
    }
}
