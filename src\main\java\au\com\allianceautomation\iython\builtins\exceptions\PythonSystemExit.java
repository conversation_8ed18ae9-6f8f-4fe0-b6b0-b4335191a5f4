package au.com.allianceautomation.iython.builtins.exceptions;

import java.util.List;

/**
 * Raised by the sys.exit() function.
 * This corresponds to Python's SystemExit.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class PythonSystemExit extends PythonBaseException {
    
    private static final long serialVersionUID = 1L;
    
    private Object code;
    
    /**
     * Constructs a new PythonSystemExit with no arguments.
     */
    public PythonSystemExit() {
        super();
        this.code = null;
    }
    
    /**
     * Constructs a new PythonSystemExit with the specified exit code.
     * 
     * @param code the exit code
     */
    public PythonSystemExit(Object code) {
        super(code != null ? String.valueOf(code) : "");
        this.code = code;
    }
    
    /**
     * Constructs a new PythonSystemExit with the specified arguments.
     * 
     * @param args the exception arguments
     */
    public PythonSystemExit(Object... args) {
        super(args);
        this.code = args.length > 0 ? args[0] : null;
    }
    
    /**
     * Constructs a new PythonSystemExit with the specified arguments.
     * 
     * @param args the exception arguments
     */
    public PythonSystemExit(List<Object> args) {
        super(args);
        this.code = args.size() > 0 ? args.get(0) : null;
    }
    
    /**
     * Get the exit code.
     * 
     * @return the exit code
     */
    public Object getCode() {
        return code;
    }
    
    /**
     * Set the exit code.
     * 
     * @param code the exit code
     */
    public void setCode(Object code) {
        this.code = code;
    }
    
    @Override
    public String getPythonClassName() {
        return "SystemExit";
    }
}
