package au.com.allianceautomation.iython.builtins.functions;

import java.util.List;

import au.com.allianceautomation.iython.builtins.AbstractBuiltinFunction;
import au.com.allianceautomation.iython.builtins.BuiltinFunction;
import au.com.allianceautomation.iython.builtins.BuiltinRegistry;

/**
 * Python help() builtin function.
 * 
 * Invokes the built-in help system. If no argument is given, the interactive help system starts.
 * If the argument is a string, then the string is looked up as the name of a module, function, class, method, keyword, or documentation topic.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class HelpFunction extends AbstractBuiltinFunction {
    
    public HelpFunction() {
        super("help", 0, 1, "help([object]) -> None");
    }
    
    @Override
    protected Object execute(List<Object> args) {
        if (args.isEmpty()) {
            // Interactive help system
            printInteractiveHelp();
        } else {
            Object obj = args.get(0);
            printHelp(obj);
        }
        
        return null; // help() returns None
    }
    
    private void printInteractiveHelp() {
        System.out.println();
        System.out.println("Welcome to iython help utility!");
        System.out.println();
        System.out.println("If this is your first time using iython, you should definitely check out");
        System.out.println("the tutorial on the Internet at https://docs.python.org/3/tutorial/.");
        System.out.println();
        System.out.println("Enter the name of any module, keyword, or topic to get help on writing");
        System.out.println("iython programs and using iython modules. To quit this help utility and");
        System.out.println("return to the interpreter, just type \"quit\".");
        System.out.println();
        System.out.println("To get a list of available modules, keywords, symbols, or topics, type");
        System.out.println("\"modules\", \"keywords\", \"symbols\", or \"topics\". Each module also comes");
        System.out.println("with a one-line summary of what it does; to list the modules whose name");
        System.out.println("or summary contain a given string such as \"spam\", type \"modules spam\".");
        System.out.println();
    }
    
    private void printHelp(Object obj) {
        if (obj instanceof String) {
            String name = (String) obj;
            
            // Check if it's a builtin function
            if (BuiltinRegistry.isBuiltin(name)) {
                BuiltinFunction func = BuiltinRegistry.getBuiltin(name);
                printBuiltinFunctionHelp(func);
                return;
            }
            
            // Handle special help topics
            switch (name.toLowerCase()) {
                case "modules":
                    printModulesHelp();
                    break;
                case "keywords":
                    printKeywordsHelp();
                    break;
                case "symbols":
                    printSymbolsHelp();
                    break;
                case "topics":
                    printTopicsHelp();
                    break;
                default:
                    System.out.println("No Python documentation found for '" + name + "'.");
                    System.out.println("Use help() to get the interactive help utility.");
                    System.out.println("Use help(str) for help on the str class.");
            }
        } else if (obj instanceof BuiltinFunction) {
            BuiltinFunction func = (BuiltinFunction) obj;
            printBuiltinFunctionHelp(func);
        } else {
            // General object help
            printObjectHelp(obj);
        }
    }
    
    private void printBuiltinFunctionHelp(BuiltinFunction func) {
        System.out.println("Help on built-in function " + func.getName() + ":");
        System.out.println();
        System.out.println(func.getDescription());
        System.out.println();
    }
    
    private void printModulesHelp() {
        System.out.println("Available modules:");
        System.out.println();
        System.out.println("builtins    - Built-in functions and exceptions");
        System.out.println("sys         - System-specific parameters and functions");
        System.out.println("os          - Operating system interface");
        System.out.println("math        - Mathematical functions");
        System.out.println();
        System.out.println("Enter any module name to get more help. Or, type \"modules spam\" to search");
        System.out.println("for modules whose name or summary contain the string \"spam\".");
        System.out.println();
    }
    
    private void printKeywordsHelp() {
        System.out.println("Here is a list of the Python keywords. Enter any keyword to get more help.");
        System.out.println();
        System.out.println("False      None       True       and        as         assert     break");
        System.out.println("class      continue   def        del        elif       else       except");
        System.out.println("finally    for        from       global     if         import     in");
        System.out.println("is         lambda     nonlocal   not        or         pass       raise");
        System.out.println("return     try        while      with       yield");
        System.out.println();
    }
    
    private void printSymbolsHelp() {
        System.out.println("Here is a list of the punctuation symbols which Python assigns special meaning");
        System.out.println("to. Enter any symbol to get more help.");
        System.out.println();
        System.out.println("!=         %          %=         &          &=         (          )");
        System.out.println("*          **         **=        *=         +          +=         ,");
        System.out.println("-          -=         ->         .          ...        /          //");
        System.out.println("//=        /=         :          ;          <          <<         <<=");
        System.out.println("<=         =          ==         >          >=         >>         >>=");
        System.out.println("@          @=         [          \\          ]          ^          ^=");
        System.out.println("{          |          |=         }          ~");
        System.out.println();
    }
    
    private void printTopicsHelp() {
        System.out.println("Here is a list of available topics. Enter any topic name to get more help.");
        System.out.println();
        System.out.println("ASSERTION           ASSIGNMENT          ATTRIBUTEMETHODS    ATTRIBUTES");
        System.out.println("AUGMENTEDASSIGNMENT BASICMETHODS        BINARY              BITWISE");
        System.out.println("BOOLEAN             CALLABLEMETHODS     CALLS               CLASSES");
        System.out.println("CODEOBJECTS         COMPARISON          COMPLEX             CONDITIONAL");
        System.out.println("CONTEXTMANAGERS     CONVERSIONS         DEBUGGING           DELETION");
        System.out.println("DICTIONARIES        DICTIONARYLITERALS  DYNAMICFEATURES     ELLIPSIS");
        System.out.println("EXCEPTIONS          EXECUTION           EXPRESSIONS         FLOAT");
        System.out.println("FORMATTING          FRAMEOBJECTS        FUNCTIONS           GENERATORS");
        System.out.println("IDENTIFIERS         IMPORTING           INTEGER             LISTLITERALS");
        System.out.println("LISTS               LITERALS            LOOPING             MAPPINGMETHODS");
        System.out.println("MAPPINGS            METHODS             MODULES             NAMESPACES");
        System.out.println("NONE                NUMBERMETHODS       NUMBERS             OBJECTS");
        System.out.println("OPERATORS           PACKAGES            POWER               PRECEDENCE");
        System.out.println("PRIVATENAMES        RETURNING           SCOPING             SEQUENCEMETHODS");
        System.out.println("SEQUENCES           SLICINGS            SPECIALATTRIBUTES   SPECIALIDENTIFIERS");
        System.out.println("SPECIALMETHODS      STRINGMETHODS       STRINGS             SUBSCRIPTS");
        System.out.println("TRACEBACKS          TRUTHVALUE          TUPLELITERALS       TUPLES");
        System.out.println("TYPEOBJECTS         TYPES               UNARY               UNICODE");
        System.out.println();
    }
    
    private void printObjectHelp(Object obj) {
        System.out.println("Help on " + obj.getClass().getSimpleName() + " object:");
        System.out.println();
        System.out.println("class " + obj.getClass().getSimpleName());
        System.out.println(" |  " + obj.toString());
        System.out.println(" |");
        System.out.println(" |  Methods defined here:");
        System.out.println(" |");
        System.out.println(" |  __str__(self)");
        System.out.println(" |      Return str(self).");
        System.out.println(" |");
        System.out.println(" |  __repr__(self)");
        System.out.println(" |      Return repr(self).");
        System.out.println();
    }
}
