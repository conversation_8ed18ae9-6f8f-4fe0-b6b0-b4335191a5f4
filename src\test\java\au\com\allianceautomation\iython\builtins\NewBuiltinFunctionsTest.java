package au.com.allianceautomation.iython.builtins;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

/**
 * Test class for newly implemented builtin functions.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class NewBuiltinFunctionsTest {
    
    @Test
    @DisplayName("Test new builtin functions are registered")
    void testNewBuiltinFunctionsRegistered() {
        // Test that all new functions are registered
        assertTrue(BuiltinRegistry.isBuiltin("all"));
        assertTrue(BuiltinRegistry.isBuiltin("any"));
        assertTrue(BuiltinRegistry.isBuiltin("ascii"));
        assertTrue(BuiltinRegistry.isBuiltin("callable"));
        assertTrue(BuiltinRegistry.isBuiltin("filter"));
        assertTrue(BuiltinRegistry.isBuiltin("format"));
        assertTrue(BuiltinRegistry.isBuiltin("hash"));
        assertTrue(BuiltinRegistry.isBuiltin("id"));
        assertTrue(BuiltinRegistry.isBuiltin("iter"));
        assertTrue(BuiltinRegistry.isBuiltin("map"));
        assertTrue(BuiltinRegistry.isBuiltin("next"));

        // Test additional new functions
        assertTrue(BuiltinRegistry.isBuiltin("input"));
        assertTrue(BuiltinRegistry.isBuiltin("open"));
        assertTrue(BuiltinRegistry.isBuiltin("globals"));
        assertTrue(BuiltinRegistry.isBuiltin("locals"));
        assertTrue(BuiltinRegistry.isBuiltin("vars"));
        assertTrue(BuiltinRegistry.isBuiltin("eval"));
        assertTrue(BuiltinRegistry.isBuiltin("exec"));

        // Test remaining high-priority functions
        assertTrue(BuiltinRegistry.isBuiltin("bytearray"));
        assertTrue(BuiltinRegistry.isBuiltin("bytes"));
        assertTrue(BuiltinRegistry.isBuiltin("complex"));
        assertTrue(BuiltinRegistry.isBuiltin("frozenset"));
        assertTrue(BuiltinRegistry.isBuiltin("memoryview"));
        assertTrue(BuiltinRegistry.isBuiltin("object"));
        assertTrue(BuiltinRegistry.isBuiltin("property"));
        assertTrue(BuiltinRegistry.isBuiltin("classmethod"));
        assertTrue(BuiltinRegistry.isBuiltin("staticmethod"));
        assertTrue(BuiltinRegistry.isBuiltin("slice"));
        assertTrue(BuiltinRegistry.isBuiltin("super"));
        assertTrue(BuiltinRegistry.isBuiltin("compile"));
        assertTrue(BuiltinRegistry.isBuiltin("delattr"));
        assertTrue(BuiltinRegistry.isBuiltin("help"));

        // Test that they return non-null functions
        assertNotNull(BuiltinRegistry.getBuiltin("all"));
        assertNotNull(BuiltinRegistry.getBuiltin("any"));
        assertNotNull(BuiltinRegistry.getBuiltin("ascii"));
        assertNotNull(BuiltinRegistry.getBuiltin("callable"));
        assertNotNull(BuiltinRegistry.getBuiltin("filter"));
        assertNotNull(BuiltinRegistry.getBuiltin("format"));
        assertNotNull(BuiltinRegistry.getBuiltin("hash"));
        assertNotNull(BuiltinRegistry.getBuiltin("id"));
        assertNotNull(BuiltinRegistry.getBuiltin("iter"));
        assertNotNull(BuiltinRegistry.getBuiltin("map"));
        assertNotNull(BuiltinRegistry.getBuiltin("next"));
    }
    
    @Test
    @DisplayName("Test all() function")
    void testAllFunction() {
        BuiltinFunction allFunc = BuiltinRegistry.getBuiltin("all");
        
        // Test with all true values
        List<Object> allTrue = Arrays.asList(true, 1, "hello", Arrays.asList(1, 2, 3));
        Object result = allFunc.call(Arrays.asList(allTrue));
        assertTrue((Boolean) result);
        
        // Test with one false value
        List<Object> oneFalse = Arrays.asList(true, 1, "", Arrays.asList(1, 2, 3));
        result = allFunc.call(Arrays.asList(oneFalse));
        assertFalse((Boolean) result);
        
        // Test with empty list
        List<Object> empty = Arrays.asList();
        result = allFunc.call(Arrays.asList(empty));
        assertTrue((Boolean) result);
    }
    
    @Test
    @DisplayName("Test any() function")
    void testAnyFunction() {
        BuiltinFunction anyFunc = BuiltinRegistry.getBuiltin("any");
        
        // Test with one true value
        List<Object> oneTrue = Arrays.asList(false, 0, "", 1);
        Object result = anyFunc.call(Arrays.asList(oneTrue));
        assertTrue((Boolean) result);
        
        // Test with all false values
        List<Object> allFalse = Arrays.asList(false, 0, "");
        result = anyFunc.call(Arrays.asList(allFalse));
        assertFalse((Boolean) result);
        
        // Test with empty list
        List<Object> empty = Arrays.asList();
        result = anyFunc.call(Arrays.asList(empty));
        assertFalse((Boolean) result);
    }
    
    @Test
    @DisplayName("Test id() function")
    void testIdFunction() {
        BuiltinFunction idFunc = BuiltinRegistry.getBuiltin("id");
        
        // Test with different objects
        String str1 = "hello";
        String str2 = "hello";
        Integer int1 = 42;
        
        Object id1 = idFunc.call(Arrays.asList(str1));
        Object id2 = idFunc.call(Arrays.asList(str2));
        Object id3 = idFunc.call(Arrays.asList(int1));
        
        // IDs should be integers
        assertTrue(id1 instanceof Integer);
        assertTrue(id2 instanceof Integer);
        assertTrue(id3 instanceof Integer);
        
        // Test with null
        Object nullId = idFunc.call(Arrays.asList((Object) null));
        assertEquals(0, nullId);
    }
    
    @Test
    @DisplayName("Test callable() function")
    void testCallableFunction() {
        BuiltinFunction callableFunc = BuiltinRegistry.getBuiltin("callable");
        
        // Test with builtin function
        BuiltinFunction printFunc = BuiltinRegistry.getBuiltin("print");
        Object result = callableFunc.call(Arrays.asList(printFunc));
        assertTrue((Boolean) result);
        
        // Test with non-callable object
        result = callableFunc.call(Arrays.asList("hello"));
        assertFalse((Boolean) result);
        
        // Test with null
        result = callableFunc.call(Arrays.asList((Object) null));
        assertFalse((Boolean) result);
    }
    
    @Test
    @DisplayName("Test hash() function")
    void testHashFunction() {
        BuiltinFunction hashFunc = BuiltinRegistry.getBuiltin("hash");
        
        // Test with string
        Object result = hashFunc.call(Arrays.asList("hello"));
        assertTrue(result instanceof Integer);
        
        // Test with integer
        result = hashFunc.call(Arrays.asList(42));
        assertTrue(result instanceof Integer);
        
        // Test with null
        result = hashFunc.call(Arrays.asList((Object) null));
        assertEquals(0, result);
    }
    
    @Test
    @DisplayName("Test ascii() function")
    void testAsciiFunction() {
        BuiltinFunction asciiFunc = BuiltinRegistry.getBuiltin("ascii");
        
        // Test with simple string
        Object result = asciiFunc.call(Arrays.asList("hello"));
        assertTrue(result instanceof String);
        assertEquals("'hello'", result);
        
        // Test with null
        result = asciiFunc.call(Arrays.asList((Object) null));
        assertEquals("None", result);
    }
    
    @Test
    @DisplayName("Test format() function")
    void testFormatFunction() {
        BuiltinFunction formatFunc = BuiltinRegistry.getBuiltin("format");
        
        // Test with simple value
        Object result = formatFunc.call(Arrays.asList("hello"));
        assertTrue(result instanceof String);
        assertEquals("hello", result);
        
        // Test with number
        result = formatFunc.call(Arrays.asList(42));
        assertTrue(result instanceof String);
        assertEquals("42", result);
    }
}
