package au.com.allianceautomation.iython.builtins.exceptions;

import java.util.List;

/**
 * This exception is derived from RuntimeError. In user defined base classes, 
 * abstract methods should raise this exception when they require derived classes 
 * to override the method, or while the class is being developed to indicate that 
 * the real implementation still needs to be added.
 * This corresponds to Python's NotImplementedError.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class PythonNotImplementedError extends PythonRuntimeError {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * Constructs a new PythonNotImplementedError with no arguments.
     */
    public PythonNotImplementedError() {
        super();
    }
    
    /**
     * Constructs a new PythonNotImplementedError with the specified message.
     * 
     * @param message the detail message
     */
    public PythonNotImplementedError(String message) {
        super(message);
    }
    
    /**
     * Constructs a new PythonNotImplementedError with the specified arguments.
     * 
     * @param args the exception arguments
     */
    public PythonNotImplementedError(Object... args) {
        super(args);
    }
    
    /**
     * Constructs a new PythonNotImplementedError with the specified arguments.
     * 
     * @param args the exception arguments
     */
    public PythonNotImplementedError(List<Object> args) {
        super(args);
    }
    
    @Override
    public String getPythonClassName() {
        return "NotImplementedError";
    }
}
