package au.com.allianceautomation.iython.builtins.functions;

import au.com.allianceautomation.iython.builtins.AbstractBuiltinFunction;
import java.util.List;

/**
 * Python complex() builtin function.
 * 
 * Returns a complex number with the value real + imag*1j or converts a string 
 * or number to a complex number.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class ComplexFunction extends AbstractBuiltinFunction {
    
    public ComplexFunction() {
        super("complex", 0, 2, "complex([real[, imag]]) -> complex");
    }
    
    @Override
    protected Object execute(List<Object> args) {
        if (args.isEmpty()) {
            // complex() -> 0+0j
            return new PythonComplex(0.0, 0.0);
        }
        
        Object real = args.get(0);
        Object imag = args.size() > 1 ? args.get(1) : 0.0;
        
        if (real instanceof String) {
            // Parse complex number from string
            String str = ((String) real).trim().replace(" ", "");
            if (args.size() > 1) {
                throw new RuntimeException("TypeError: complex() can't take second arg if first is a string");
            }
            return parseComplexString(str);
        }
        
        double realPart = convertToDouble(real);
        double imagPart = convertToDouble(imag);
        
        return new PythonComplex(realPart, imagPart);
    }
    
    private double convertToDouble(Object obj) {
        if (obj instanceof Number) {
            return ((Number) obj).doubleValue();
        } else if (obj instanceof String) {
            try {
                return Double.parseDouble((String) obj);
            } catch (NumberFormatException e) {
                throw new RuntimeException("ValueError: could not convert string to float: " + obj);
            }
        } else {
            throw new RuntimeException("TypeError: complex() argument must be a string or a number, not '" + 
                                     obj.getClass().getSimpleName() + "'");
        }
    }
    
    private PythonComplex parseComplexString(String str) {
        try {
            // Handle simple cases
            if (str.equals("j") || str.equals("1j")) {
                return new PythonComplex(0.0, 1.0);
            } else if (str.equals("-j") || str.equals("-1j")) {
                return new PythonComplex(0.0, -1.0);
            }
            
            // Remove parentheses if present
            if (str.startsWith("(") && str.endsWith(")")) {
                str = str.substring(1, str.length() - 1);
            }
            
            // Check for 'j' at the end
            if (str.endsWith("j")) {
                String numPart = str.substring(0, str.length() - 1);
                if (numPart.isEmpty() || numPart.equals("+")) {
                    return new PythonComplex(0.0, 1.0);
                } else if (numPart.equals("-")) {
                    return new PythonComplex(0.0, -1.0);
                } else {
                    // Pure imaginary number
                    double imag = Double.parseDouble(numPart);
                    return new PythonComplex(0.0, imag);
                }
            }
            
            // Check for real + imaginary format
            int plusIndex = str.lastIndexOf('+');
            int minusIndex = str.lastIndexOf('-');
            
            if (plusIndex > 0 || (minusIndex > 0 && str.charAt(0) != '-')) {
                int splitIndex = Math.max(plusIndex, minusIndex);
                String realPart = str.substring(0, splitIndex);
                String imagPart = str.substring(splitIndex);
                
                if (imagPart.endsWith("j")) {
                    imagPart = imagPart.substring(0, imagPart.length() - 1);
                    if (imagPart.equals("+") || imagPart.isEmpty()) {
                        imagPart = "1";
                    } else if (imagPart.equals("-")) {
                        imagPart = "-1";
                    }
                    
                    double real = Double.parseDouble(realPart);
                    double imag = Double.parseDouble(imagPart);
                    return new PythonComplex(real, imag);
                }
            }
            
            // Pure real number
            double real = Double.parseDouble(str);
            return new PythonComplex(real, 0.0);
            
        } catch (NumberFormatException e) {
            throw new RuntimeException("ValueError: complex() arg is a malformed string");
        }
    }
    
    /**
     * Complex number representation for Python.
     */
    public static class PythonComplex {
        private final double real;
        private final double imag;
        
        public PythonComplex(double real, double imag) {
            this.real = real;
            this.imag = imag;
        }
        
        public double getReal() {
            return real;
        }
        
        public double getImag() {
            return imag;
        }
        
        public PythonComplex add(PythonComplex other) {
            return new PythonComplex(real + other.real, imag + other.imag);
        }
        
        public PythonComplex subtract(PythonComplex other) {
            return new PythonComplex(real - other.real, imag - other.imag);
        }
        
        public PythonComplex multiply(PythonComplex other) {
            double newReal = real * other.real - imag * other.imag;
            double newImag = real * other.imag + imag * other.real;
            return new PythonComplex(newReal, newImag);
        }
        
        public double abs() {
            return Math.sqrt(real * real + imag * imag);
        }
        
        @Override
        public String toString() {
            if (imag == 0.0) {
                if (real == (long) real) {
                    return "(" + (long) real + "+0j)";
                } else {
                    return "(" + real + "+0j)";
                }
            } else if (real == 0.0) {
                if (imag == 1.0) {
                    return "1j";
                } else if (imag == -1.0) {
                    return "-1j";
                } else if (imag == (long) imag) {
                    return (long) imag + "j";
                } else {
                    return imag + "j";
                }
            } else {
                String realStr = real == (long) real ? String.valueOf((long) real) : String.valueOf(real);
                String imagStr;
                if (imag == 1.0) {
                    imagStr = "+1j";
                } else if (imag == -1.0) {
                    imagStr = "-1j";
                } else if (imag > 0) {
                    imagStr = imag == (long) imag ? "+" + (long) imag + "j" : "+" + imag + "j";
                } else {
                    imagStr = imag == (long) imag ? String.valueOf((long) imag) + "j" : imag + "j";
                }
                return "(" + realStr + imagStr + ")";
            }
        }
        
        @Override
        public boolean equals(Object obj) {
            if (this == obj) return true;
            if (!(obj instanceof PythonComplex)) return false;
            PythonComplex other = (PythonComplex) obj;
            return Double.compare(real, other.real) == 0 && Double.compare(imag, other.imag) == 0;
        }
        
        @Override
        public int hashCode() {
            return Double.hashCode(real) ^ Double.hashCode(imag);
        }
    }
}
