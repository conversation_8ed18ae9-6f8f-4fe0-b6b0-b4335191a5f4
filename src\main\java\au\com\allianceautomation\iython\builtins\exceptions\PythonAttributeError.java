package au.com.allianceautomation.iython.builtins.exceptions;

import java.util.List;

/**
 * Raised when an attribute reference or assignment fails.
 * This corresponds to Python's AttributeError.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class PythonAttributeError extends PythonException {
    
    private static final long serialVersionUID = 1L;
    
    private String name;
    private Object obj;
    
    /**
     * Constructs a new PythonAttributeError with no arguments.
     */
    public PythonAttributeError() {
        super();
    }
    
    /**
     * Constructs a new PythonAttributeError with the specified message.
     * 
     * @param message the detail message
     */
    public PythonAttributeError(String message) {
        super(message);
    }
    
    /**
     * Constructs a new PythonAttributeError with the specified message, name, and object.
     * 
     * @param message the detail message
     * @param name the name of the attribute that was attempted to be accessed
     * @param obj the object that was accessed for said attribute
     */
    public PythonAttributeError(String message, String name, Object obj) {
        super(message);
        this.name = name;
        this.obj = obj;
    }
    
    /**
     * Constructs a new PythonAttributeError with the specified arguments.
     * 
     * @param args the exception arguments
     */
    public PythonAttributeError(Object... args) {
        super(args);
    }
    
    /**
     * Constructs a new PythonAttributeError with the specified arguments.
     * 
     * @param args the exception arguments
     */
    public PythonAttributeError(List<Object> args) {
        super(args);
    }
    
    /**
     * Get the name of the attribute that was attempted to be accessed.
     * 
     * @return the attribute name, or null if not set
     */
    public String getName() {
        return name;
    }
    
    /**
     * Set the name of the attribute that was attempted to be accessed.
     * 
     * @param name the attribute name
     */
    public void setName(String name) {
        this.name = name;
    }
    
    /**
     * Get the object that was accessed for the attribute.
     * 
     * @return the object, or null if not set
     */
    public Object getObj() {
        return obj;
    }
    
    /**
     * Set the object that was accessed for the attribute.
     * 
     * @param obj the object
     */
    public void setObj(Object obj) {
        this.obj = obj;
    }
    
    @Override
    public String getPythonClassName() {
        return "AttributeError";
    }
}
