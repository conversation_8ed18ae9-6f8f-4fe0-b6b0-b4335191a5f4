package au.com.allianceautomation.iython.builtins.functions;

import au.com.allianceautomation.iython.builtins.AbstractBuiltinFunction;
import java.util.List;
import java.io.*;
import java.nio.file.Files;
import java.nio.file.Paths;

/**
 * Python open() builtin function.
 * 
 * Opens a file and returns a corresponding file object.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class OpenFunction extends AbstractBuiltinFunction {
    
    public OpenFunction() {
        super("open", 1, 4, "open(file, mode='r', buffering=-1, encoding=None) -> file object");
    }
    
    @Override
    protected Object execute(List<Object> args) {
        String filename = pythonStr(args.get(0));
        String mode = args.size() > 1 ? pythonStr(args.get(1)) : "r";
        
        try {
            if (mode.equals("r") || mode.equals("rt")) {
                // Read text mode
                return new PythonFileReader(filename);
            } else if (mode.equals("w") || mode.equals("wt")) {
                // Write text mode
                return new PythonFileWriter(filename);
            } else if (mode.equals("a") || mode.equals("at")) {
                // Append text mode
                return new PythonFileWriter(filename, true);
            } else {
                throw new RuntimeException("Unsupported file mode: " + mode);
            }
        } catch (IOException e) {
            throw new RuntimeException("FileNotFoundError: [Errno 2] No such file or directory: '" + filename + "'");
        }
    }
    
    /**
     * Simple file reader wrapper for Python file objects.
     */
    public static class PythonFileReader {
        private final BufferedReader reader;
        private final String filename;
        
        public PythonFileReader(String filename) throws IOException {
            this.filename = filename;
            this.reader = Files.newBufferedReader(Paths.get(filename));
        }
        
        public String read() throws IOException {
            StringBuilder content = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                content.append(line).append("\n");
            }
            return content.toString();
        }
        
        public String readline() throws IOException {
            String line = reader.readLine();
            return line != null ? line + "\n" : "";
        }
        
        public void close() throws IOException {
            reader.close();
        }
        
        @Override
        public String toString() {
            return "<_io.TextIOWrapper name='" + filename + "' mode='r' encoding='UTF-8'>";
        }
    }
    
    /**
     * Simple file writer wrapper for Python file objects.
     */
    public static class PythonFileWriter {
        private final BufferedWriter writer;
        private final String filename;
        
        public PythonFileWriter(String filename) throws IOException {
            this.filename = filename;
            this.writer = Files.newBufferedWriter(Paths.get(filename));
        }
        
        public PythonFileWriter(String filename, boolean append) throws IOException {
            this.filename = filename;
            if (append) {
                this.writer = new BufferedWriter(new FileWriter(filename, true));
            } else {
                this.writer = Files.newBufferedWriter(Paths.get(filename));
            }
        }
        
        public void write(String text) throws IOException {
            writer.write(text);
        }
        
        public void flush() throws IOException {
            writer.flush();
        }
        
        public void close() throws IOException {
            writer.close();
        }
        
        @Override
        public String toString() {
            return "<_io.TextIOWrapper name='" + filename + "' mode='w' encoding='UTF-8'>";
        }
    }
}
