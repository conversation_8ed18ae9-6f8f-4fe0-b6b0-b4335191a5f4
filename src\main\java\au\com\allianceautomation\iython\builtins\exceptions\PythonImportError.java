package au.com.allianceautomation.iython.builtins.exceptions;

import java.util.List;

/**
 * Raised when the import statement has troubles trying to load a module.
 * This corresponds to Python's ImportError.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class PythonImportError extends PythonException {
    
    private static final long serialVersionUID = 1L;
    
    private String name;
    private String path;
    
    /**
     * Constructs a new PythonImportError with no arguments.
     */
    public PythonImportError() {
        super();
    }
    
    /**
     * Constructs a new PythonImportError with the specified message.
     * 
     * @param message the detail message
     */
    public PythonImportError(String message) {
        super(message);
    }
    
    /**
     * Constructs a new PythonImportError with the specified message, name, and path.
     * 
     * @param message the detail message
     * @param name the name of the module that was attempted to be imported
     * @param path the path to any file which triggered the exception
     */
    public PythonImportError(String message, String name, String path) {
        super(message);
        this.name = name;
        this.path = path;
    }
    
    /**
     * Constructs a new PythonImportError with the specified arguments.
     * 
     * @param args the exception arguments
     */
    public PythonImportError(Object... args) {
        super(args);
    }
    
    /**
     * Constructs a new PythonImportError with the specified arguments.
     * 
     * @param args the exception arguments
     */
    public PythonImportError(List<Object> args) {
        super(args);
    }
    
    /**
     * Get the name of the module that was attempted to be imported.
     * 
     * @return the module name, or null if not set
     */
    public String getName() {
        return name;
    }
    
    /**
     * Set the name of the module that was attempted to be imported.
     * 
     * @param name the module name
     */
    public void setName(String name) {
        this.name = name;
    }
    
    /**
     * Get the path to any file which triggered the exception.
     * 
     * @return the path, or null if not set
     */
    public String getPath() {
        return path;
    }
    
    /**
     * Set the path to any file which triggered the exception.
     * 
     * @param path the path
     */
    public void setPath(String path) {
        this.path = path;
    }
    
    @Override
    public String getPythonClassName() {
        return "ImportError";
    }
}
