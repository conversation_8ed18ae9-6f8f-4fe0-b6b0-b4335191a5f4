package au.com.allianceautomation.iython.builtins;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * Represents the Python __builtins__ module.
 * This class provides access to all builtin functions and exceptions and serves as the
 * object returned when accessing __builtins__ in Python code.
 *
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class BuiltinsModule {
    
    private static final BuiltinsModule INSTANCE = new BuiltinsModule();
    
    private BuiltinsModule() {
        // Private constructor for singleton
    }
    
    /**
     * Get the singleton instance of the builtins module.
     * 
     * @return The builtins module instance
     */
    public static BuiltinsModule getInstance() {
        return INSTANCE;
    }
    
    /**
     * Get a builtin function by name.
     * 
     * @param name The name of the builtin function
     * @return The builtin function, or null if not found
     */
    public BuiltinFunction getBuiltin(String name) {
        return BuiltinRegistry.getBuiltin(name);
    }
    
    /**
     * Check if a name is a builtin function.
     *
     * @param name The name to check
     * @return true if it's a builtin function
     */
    public boolean hasBuiltin(String name) {
        return BuiltinRegistry.isBuiltin(name);
    }

    /**
     * Get an exception class by name.
     *
     * @param name The name of the exception
     * @return The exception class, or null if not found
     */
    public Class<?> getException(String name) {
        return ExceptionRegistry.getException(name);
    }

    /**
     * Check if a name is a builtin exception.
     *
     * @param name The name to check
     * @return true if it's a builtin exception
     */
    public boolean hasException(String name) {
        return ExceptionRegistry.isException(name);
    }
    
    /**
     * Get all builtin names (functions and exceptions) as a sorted list.
     * This is used by dir(__builtins__).
     *
     * @return A sorted list of all builtin names
     */
    public List<String> getBuiltinNames() {
        Set<String> allNames = new HashSet<>();
        allNames.addAll(BuiltinRegistry.getBuiltinNames());
        allNames.addAll(ExceptionRegistry.getExceptionNames());

        List<String> sortedNames = new ArrayList<>(allNames);
        Collections.sort(sortedNames);
        return sortedNames;
    }
    
    /**
     * Get the total number of builtin functions and exceptions.
     *
     * @return The number of builtin functions and exceptions
     */
    public int getBuiltinCount() {
        return BuiltinRegistry.getBuiltinCount() + ExceptionRegistry.getExceptionCount();
    }
    
    @Override
    public String toString() {
        return "<module 'builtins' (built-in)>";
    }
}
