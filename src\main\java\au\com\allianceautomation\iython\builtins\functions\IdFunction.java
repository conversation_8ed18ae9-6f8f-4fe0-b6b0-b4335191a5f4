package au.com.allianceautomation.iython.builtins.functions;

import au.com.allianceautomation.iython.builtins.AbstractBuiltinFunction;
import java.util.List;

/**
 * Python id() builtin function.
 * 
 * Returns the identity of an object. This is guaranteed to be unique among 
 * simultaneously existing objects. In CPython, this is the object's memory address.
 * In iython, we use the Java object's hashCode as an approximation.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class IdFunction extends AbstractBuiltinFunction {
    
    public IdFunction() {
        super("id", 1, 1, "id(obj) -> int");
    }
    
    @Override
    protected Object execute(List<Object> args) {
        Object obj = args.get(0);
        
        if (obj == null) {
            // Return a consistent ID for None/null
            return 0;
        }
        
        // Use System.identityHashCode which is based on the object's memory address
        // This is closer to CPython's behavior than regular hashCode()
        return System.identityHashCode(obj);
    }
}
