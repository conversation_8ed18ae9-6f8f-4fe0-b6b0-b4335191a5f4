package au.com.allianceautomation.iython.builtins.exceptions.lookup;

import java.util.List;

/**
 * Raised when a mapping (dictionary) key is not found in the set of existing keys.
 * This corresponds to Python's KeyError.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class <PERSON><PERSON>eyError extends PythonLookupError {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * Constructs a new PythonKeyError with no arguments.
     */
    public PythonKeyError() {
        super();
    }
    
    /**
     * Constructs a new PythonKeyError with the specified message.
     * 
     * @param message the detail message
     */
    public PythonKeyError(String message) {
        super(message);
    }
    
    /**
     * Constructs a new PythonKeyError with the specified arguments.
     * 
     * @param args the exception arguments
     */
    public PythonKeyError(Object... args) {
        super(args);
    }
    
    /**
     * Constructs a new PythonKeyError with the specified arguments.
     * 
     * @param args the exception arguments
     */
    public PythonKeyError(List<Object> args) {
        super(args);
    }
    
    @Override
    public String getPythonClassName() {
        return "KeyError";
    }
}
