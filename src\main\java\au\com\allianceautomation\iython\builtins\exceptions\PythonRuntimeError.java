package au.com.allianceautomation.iython.builtins.exceptions;

import java.util.List;

/**
 * Raised when an error is detected that doesn't fall in any of the other categories.
 * This corresponds to Python's RuntimeError.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class PythonRuntimeError extends PythonException {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * Constructs a new PythonRuntimeError with no arguments.
     */
    public PythonRuntimeError() {
        super();
    }
    
    /**
     * Constructs a new PythonRuntimeError with the specified message.
     * 
     * @param message the detail message
     */
    public PythonRuntimeError(String message) {
        super(message);
    }
    
    /**
     * Constructs a new PythonRuntimeError with the specified arguments.
     * 
     * @param args the exception arguments
     */
    public PythonRuntimeError(Object... args) {
        super(args);
    }
    
    /**
     * Constructs a new PythonRuntimeError with the specified arguments.
     * 
     * @param args the exception arguments
     */
    public PythonRuntimeError(List<Object> args) {
        super(args);
    }
    
    @Override
    public String getPythonClassName() {
        return "RuntimeError";
    }
}
