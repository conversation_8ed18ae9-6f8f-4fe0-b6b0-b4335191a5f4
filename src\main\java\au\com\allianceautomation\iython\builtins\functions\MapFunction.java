package au.com.allianceautomation.iython.builtins.functions;

import au.com.allianceautomation.iython.builtins.AbstractBuiltinFunction;
import au.com.allianceautomation.iython.builtins.BuiltinFunction;
import java.util.List;
import java.util.ArrayList;

/**
 * Python map() builtin function.
 * 
 * Returns an iterator that applies function to every item of iterable, yielding the results.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class MapFunction extends AbstractBuiltinFunction {
    
    public MapFunction() {
        super("map", 2, 2, "map(function, iterable) -> iterator");
    }
    
    @Override
    protected Object execute(List<Object> args) {
        Object function = args.get(0);
        Object iterable = args.get(1);
        
        List<Object> result = new ArrayList<>();
        
        // Convert iterable to list for processing
        List<Object> items = convertToList(iterable);
        
        for (Object item : items) {
            Object mappedValue;
            
            if (function instanceof BuiltinFunction) {
                // Call the builtin function
                BuiltinFunction builtinFunc = (BuiltinFunction) function;
                mappedValue = builtinFunc.call(List.of(item));
            } else {
                // For now, assume other callables are not supported
                throw new RuntimeException("Function type not supported in map()");
            }
            
            result.add(mappedValue);
        }
        
        return result;
    }
    
    private List<Object> convertToList(Object iterable) {
        if (iterable instanceof List) {
            return new ArrayList<>((List<?>) iterable);
        } else if (iterable instanceof String) {
            String str = (String) iterable;
            List<Object> result = new ArrayList<>();
            for (char c : str.toCharArray()) {
                result.add(String.valueOf(c));
            }
            return result;
        } else if (iterable.getClass().isArray()) {
            Object[] array = (Object[]) iterable;
            List<Object> result = new ArrayList<>();
            for (Object item : array) {
                result.add(item);
            }
            return result;
        } else {
            throw new RuntimeException("'" + iterable.getClass().getSimpleName() + "' object is not iterable");
        }
    }
}
