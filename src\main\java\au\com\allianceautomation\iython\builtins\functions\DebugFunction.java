package au.com.allianceautomation.iython.builtins.functions;

import au.com.allianceautomation.iython.builtins.AbstractBuiltinFunction;
import java.util.List;

/**
 * Python __debug__ builtin constant.
 * 
 * This is a built-in constant that is True if Python was not started with an -O option.
 * In CPython, this is used for debugging assertions and other debug code.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class DebugFunction extends AbstractBuiltinFunction {
    
    public DebugFunction() {
        super("__debug__", 0, 0, "__debug__ -> bool");
    }
    
    @Override
    protected Object execute(List<Object> args) {
        // In iython, we'll default to True (debug mode enabled)
        // This could be made configurable in the future
        return true;
    }
}
