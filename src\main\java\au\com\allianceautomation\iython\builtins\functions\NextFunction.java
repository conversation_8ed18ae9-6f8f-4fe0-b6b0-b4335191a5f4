package au.com.allianceautomation.iython.builtins.functions;

import au.com.allianceautomation.iython.builtins.AbstractBuiltinFunction;
import java.util.List;
import java.util.Iterator;

/**
 * Python next() builtin function.
 * 
 * Retrieves the next item from the iterator by calling its __next__() method.
 * If default is given, it is returned if the iterator is exhausted, 
 * otherwise StopIteration is raised.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class NextFunction extends AbstractBuiltinFunction {
    
    public NextFunction() {
        super("next", 1, 2, "next(iterator[, default]) -> value");
    }
    
    @Override
    protected Object execute(List<Object> args) {
        Object iterator = args.get(0);
        Object defaultValue = args.size() > 1 ? args.get(1) : null;
        boolean hasDefault = args.size() > 1;
        
        if (iterator instanceof Iterator) {
            Iterator<?> iter = (Iterator<?>) iterator;
            if (iter.hasNext()) {
                return iter.next();
            } else {
                if (hasDefault) {
                    return defaultValue;
                } else {
                    throw new RuntimeException("StopIteration");
                }
            }
        } else if (iterator instanceof List) {
            // For lists, we need to maintain state somehow
            // This is a simplified implementation
            List<?> list = (List<?>) iterator;
            if (!list.isEmpty()) {
                Object first = list.get(0);
                list.remove(0); // Modify the list (not ideal, but simple)
                return first;
            } else {
                if (hasDefault) {
                    return defaultValue;
                } else {
                    throw new RuntimeException("StopIteration");
                }
            }
        } else {
            throw new RuntimeException("'" + iterator.getClass().getSimpleName() + "' object is not an iterator");
        }
    }
}
