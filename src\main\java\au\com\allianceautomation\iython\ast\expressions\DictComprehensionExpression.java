package au.com.allianceautomation.iython.ast.expressions;

import au.com.allianceautomation.iython.ast.ASTVisitor;
import java.util.List;

/**
 * AST node representing dictionary comprehensions.
 * Represents expressions like: {key_expr: value_expr for var in iterable if condition}
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class DictComprehensionExpression extends Expression {
    private final Expression key;
    private final Expression value;
    private final List<ListComprehensionExpression.ComprehensionClause> generators;
    
    public DictComprehensionExpression(int line, int column, Expression key, Expression value, 
                                     List<ListComprehensionExpression.ComprehensionClause> generators) {
        super(line, column);
        this.key = key;
        this.value = value;
        this.generators = generators;
    }
    
    public Expression getKey() { 
        return key; 
    }
    
    public Expression getValue() { 
        return value; 
    }
    
    public List<ListComprehensionExpression.ComprehensionClause> getGenerators() { 
        return generators; 
    }
    
    @Override
    public <T> T accept(ASTVisitor<T> visitor) {
        return visitor.visitDictComprehension(this);
    }
    
    @Override
    public String toString() {
        return "DictComprehension(" + key + ": " + value + " for " + generators + ")";
    }
}
