package au.com.allianceautomation.iython.builtins.functions;

import java.util.ArrayList;
import java.util.List;

import au.com.allianceautomation.iython.builtins.AbstractBuiltinFunction;
import au.com.allianceautomation.iython.builtins.BuiltinFunction;

/**
 * Python filter() builtin function.
 * 
 * Constructs an iterator from those elements of iterable for which function returns true.
 * If function is None, the identity function is assumed.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class FilterFunction extends AbstractBuiltinFunction {
    
    public FilterFunction() {
        super("filter", 2, 2, "filter(function, iterable) -> iterator");
    }
    
    @Override
    protected Object execute(List<Object> args) {
        Object function = args.get(0);
        Object iterable = args.get(1);
        
        List<Object> result = new ArrayList<>();
        
        // Convert iterable to list for processing
        List<Object> items = convertToList(iterable);
        
        for (Object item : items) {
            boolean include = false;
            
            if (function == null) {
                // If function is None, use truthiness of the item
                include = isTruthy(item);
            } else if (function instanceof BuiltinFunction) {
                // Call the builtin function
                BuiltinFunction builtinFunc = (BuiltinFunction) function;
                Object result_obj = builtinFunc.call(List.of(item));
                include = isTruthy(result_obj);
            } else {
                // For now, assume other callables are not supported
                throw new RuntimeException("Function type not supported in filter()");
            }
            
            if (include) {
                result.add(item);
            }
        }
        
        return result;
    }
    
    private List<Object> convertToList(Object iterable) {
        if (iterable instanceof List) {
            return new ArrayList<>((List<?>) iterable);
        } else if (iterable instanceof String) {
            String str = (String) iterable;
            List<Object> result = new ArrayList<>();
            for (char c : str.toCharArray()) {
                result.add(String.valueOf(c));
            }
            return result;
        } else if (iterable.getClass().isArray()) {
            Object[] array = (Object[]) iterable;
            List<Object> result = new ArrayList<>();
            for (Object item : array) {
                result.add(item);
            }
            return result;
        } else {
            throw new RuntimeException("'" + iterable.getClass().getSimpleName() + "' object is not iterable");
        }
    }
}
