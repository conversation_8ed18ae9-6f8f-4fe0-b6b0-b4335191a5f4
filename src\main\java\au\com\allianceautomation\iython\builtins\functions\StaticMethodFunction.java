package au.com.allianceautomation.iython.builtins.functions;

import au.com.allianceautomation.iython.builtins.AbstractBuiltinFunction;
import au.com.allianceautomation.iython.builtins.BuiltinFunction;
import java.util.List;

/**
 * Python staticmethod() builtin function.
 * 
 * Returns a static method for function.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class StaticMethodFunction extends AbstractBuiltinFunction {
    
    public StaticMethodFunction() {
        super("staticmethod", 1, 1, "staticmethod(function) -> method");
    }
    
    @Override
    protected Object execute(List<Object> args) {
        Object function = args.get(0);
        
        if (function == null) {
            throw new RuntimeException("TypeError: staticmethod expected 1 argument, got 0");
        }
        
        return new PythonStaticMethod(function);
    }
    
    /**
     * Static method descriptor for Python.
     */
    public static class PythonStaticMethod {
        private final Object function;
        
        public PythonStaticMethod(Object function) {
            this.function = function;
        }
        
        public Object getFunction() {
            return function;
        }
        
        public Object get(Object instance, Object owner) {
            // For static methods, return the function as-is (no binding)
            return function;
        }
        
        @Override
        public String toString() {
            return "<staticmethod object at 0x" + Integer.toHexString(System.identityHashCode(this)) + ">";
        }
    }
}
