package au.com.allianceautomation.iython.builtins.exceptions;

import java.util.List;

/**
 * Raised when an operation runs out of memory but the situation may still be rescued 
 * (by deleting some objects).
 * This corresponds to Python's MemoryError.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class PythonMemoryError extends PythonException {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * Constructs a new PythonMemoryError with no arguments.
     */
    public PythonMemoryError() {
        super();
    }
    
    /**
     * Constructs a new PythonMemoryError with the specified message.
     * 
     * @param message the detail message
     */
    public PythonMemoryError(String message) {
        super(message);
    }
    
    /**
     * Constructs a new PythonMemoryError with the specified arguments.
     * 
     * @param args the exception arguments
     */
    public PythonMemoryError(Object... args) {
        super(args);
    }
    
    /**
     * Constructs a new PythonMemoryError with the specified arguments.
     * 
     * @param args the exception arguments
     */
    public PythonMemoryError(List<Object> args) {
        super(args);
    }
    
    @Override
    public String getPythonClassName() {
        return "MemoryError";
    }
}
