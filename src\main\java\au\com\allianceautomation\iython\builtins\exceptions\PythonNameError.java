package au.com.allianceautomation.iython.builtins.exceptions;

import java.util.List;

/**
 * Raised when a local or global name is not found.
 * This applies only to unqualified names.
 * This corresponds to Python's NameError.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class PythonNameError extends PythonException {
    
    private static final long serialVersionUID = 1L;
    
    private String name;
    
    /**
     * Constructs a new PythonNameError with no arguments.
     */
    public PythonNameError() {
        super();
    }
    
    /**
     * Constructs a new PythonNameError with the specified message.
     * 
     * @param message the detail message
     */
    public PythonNameError(String message) {
        super(message);
    }
    
    /**
     * Constructs a new PythonNameError with the specified message and name.
     * 
     * @param message the detail message
     * @param name the name that could not be found
     */
    public PythonNameError(String message, String name) {
        super(message);
        this.name = name;
    }
    
    /**
     * Constructs a new PythonNameError with the specified arguments.
     * 
     * @param args the exception arguments
     */
    public PythonNameError(Object... args) {
        super(args);
    }
    
    /**
     * Constructs a new PythonNameError with the specified arguments.
     * 
     * @param args the exception arguments
     */
    public PythonNameError(List<Object> args) {
        super(args);
    }
    
    /**
     * Get the name that could not be found.
     * 
     * @return the name, or null if not set
     */
    public String getName() {
        return name;
    }
    
    /**
     * Set the name that could not be found.
     * 
     * @param name the name
     */
    public void setName(String name) {
        this.name = name;
    }
    
    @Override
    public String getPythonClassName() {
        return "NameError";
    }
}
