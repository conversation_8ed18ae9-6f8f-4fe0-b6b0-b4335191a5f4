package au.com.allianceautomation.iython.builtins.functions;

import au.com.allianceautomation.iython.builtins.AbstractBuiltinFunction;
import java.util.List;

/**
 * Python hash() builtin function.
 * 
 * Returns the hash value of the object (if it has one). Hash values are integers.
 * They are used to quickly compare dictionary keys during a dictionary lookup.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class HashFunction extends AbstractBuiltinFunction {
    
    public HashFunction() {
        super("hash", 1, 1, "hash(obj) -> int");
    }
    
    @Override
    protected Object execute(List<Object> args) {
        Object obj = args.get(0);
        
        if (obj == null) {
            // None has a hash value of 0 in Python
            return 0;
        }
        
        // Check for unhashable types
        if (obj instanceof List || obj instanceof java.util.Map || obj instanceof java.util.Set) {
            throw new RuntimeException("unhashable type: '" + obj.getClass().getSimpleName() + "'");
        }
        
        // For basic types, use Java's hashCode
        if (obj instanceof String || obj instanceof Number || obj instanceof Boolean || obj instanceof Character) {
            return obj.hashCode();
        }
        
        // For tuples (represented as arrays), compute hash based on contents
        if (obj.getClass().isArray()) {
            Object[] array = (Object[]) obj;
            int hash = 1;
            for (Object item : array) {
                // Recursively hash each element
                hash = 31 * hash + (item == null ? 0 : (Integer) execute(List.of(item)));
            }
            return hash;
        }
        
        // For other objects, use Java's hashCode as fallback
        return obj.hashCode();
    }
}
