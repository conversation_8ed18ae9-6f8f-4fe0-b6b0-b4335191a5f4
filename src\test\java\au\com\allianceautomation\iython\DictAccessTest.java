package au.com.allianceautomation.iython;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test class for dictionary access methods.
 */
public class DictAccessTest {
    
    private PythonExecutor executor;
    
    @BeforeEach
    void setUp() {
        executor = new PythonExecutor();
    }
    
    @Test
    @DisplayName("Test dictionary subscript access")
    void testDictSubscriptAccess() throws PythonExecutionException {
        String code = "d = {'name': 'Alice', 'age': 30, 'city': 'New York'}\n" +
                      "name = d['name']\n" +
                      "age = d['age']\n" +
                      "city = d['city']";
        executor.executeCode(code);

        Object name = executor.executeAndGetVariable("", "name");
        Object age = executor.executeAndGetVariable("", "age");
        Object city = executor.executeAndGetVariable("", "city");
        
        assertEquals("Alice", name);
        assertEquals(30L, age);
        assertEquals("New York", city);
    }
    
    @Test
    @DisplayName("Test dictionary get method")
    void testDictGetMethod() throws PythonExecutionException {
        String code = "d = {'a': 1, 'b': 2}\n" +
                      "value1 = d.get('a')\n" +
                      "value2 = d.get('b')\n" +
                      "value3 = d.get('c')\n" +
                      "value4 = d.get('c', 'default')";
        executor.executeCode(code);

        Object value1 = executor.executeAndGetVariable("", "value1");
        Object value2 = executor.executeAndGetVariable("", "value2");
        Object value3 = executor.executeAndGetVariable("", "value3");
        Object value4 = executor.executeAndGetVariable("", "value4");
        
        assertEquals(1L, value1);
        assertEquals(2L, value2);
        assertNull(value3);
        assertEquals("default", value4);
    }
    
    @Test
    @DisplayName("Test dictionary keys method")
    void testDictKeysMethod() throws PythonExecutionException {
        String code = "d = {'x': 10, 'y': 20, 'z': 30}\n" +
                      "keys = d.keys()";
        executor.executeCode(code);

        Object result = executor.executeAndGetVariable("", "keys");
        assertTrue(result instanceof List);
        List<?> keys = (List<?>) result;
        assertEquals(3, keys.size());
        assertTrue(keys.contains("x"));
        assertTrue(keys.contains("y"));
        assertTrue(keys.contains("z"));
    }
    
    @Test
    @DisplayName("Test dictionary values method")
    void testDictValuesMethod() throws PythonExecutionException {
        String code = "d = {'a': 100, 'b': 200, 'c': 300}\n" +
                      "values = d.values()";
        executor.executeCode(code);

        Object result = executor.executeAndGetVariable("", "values");
        assertTrue(result instanceof List);
        List<?> values = (List<?>) result;
        assertEquals(3, values.size());
        assertTrue(values.contains(100L));
        assertTrue(values.contains(200L));
        assertTrue(values.contains(300L));
    }
    
    @Test
    @DisplayName("Test dictionary items method")
    void testDictItemsMethod() throws PythonExecutionException {
        String code = "d = {'first': 1, 'second': 2}\n" +
                      "items = d.items()";
        executor.executeCode(code);

        Object result = executor.executeAndGetVariable("", "items");
        assertTrue(result instanceof List);
        List<?> items = (List<?>) result;
        assertEquals(2, items.size());
        
        // Check that each item is a list with key-value pair
        for (Object item : items) {
            assertTrue(item instanceof List);
            List<?> pair = (List<?>) item;
            assertEquals(2, pair.size());
        }
    }
    
    @Test
    @DisplayName("Test KeyError for missing key")
    void testKeyError() throws PythonExecutionException {
        String code = "d = {'a': 1}";
        executor.executeCode(code);

        assertThrows(PythonExecutionException.class, () -> {
            executor.executeCode("value = d['missing_key']");
        });
    }
    
    @Test
    @DisplayName("Test list subscript access")
    void testListSubscriptAccess() throws PythonExecutionException {
        String code = "lst = [10, 20, 30, 40, 50]\n" +
                      "first = lst[0]\n" +
                      "last = lst[-1]\n" +
                      "middle = lst[2]";
        executor.executeCode(code);

        Object first = executor.executeAndGetVariable("", "first");
        Object last = executor.executeAndGetVariable("", "last");
        Object middle = executor.executeAndGetVariable("", "middle");
        
        assertEquals(10L, first);
        assertEquals(50L, last);
        assertEquals(30L, middle);
    }
    
    @Test
    @DisplayName("Test string subscript access")
    void testStringSubscriptAccess() throws PythonExecutionException {
        String code = "s = 'Hello'\n" +
                      "first = s[0]\n" +
                      "last = s[-1]\n" +
                      "middle = s[2]";
        executor.executeCode(code);

        Object first = executor.executeAndGetVariable("", "first");
        Object last = executor.executeAndGetVariable("", "last");
        Object middle = executor.executeAndGetVariable("", "middle");
        
        assertEquals("H", first);
        assertEquals("o", last);
        assertEquals("l", middle);
    }
}
