package au.com.allianceautomation.iython.builtins.functions;

import au.com.allianceautomation.iython.builtins.AbstractBuiltinFunction;
import java.util.List;
import java.util.HashMap;
import java.util.Map;

/**
 * Python locals() builtin function.
 * 
 * Returns a dictionary representing the current local symbol table.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class LocalsFunction extends AbstractBuiltinFunction {
    
    public LocalsFunction() {
        super("locals", 0, 0, "locals() -> dict");
    }
    
    @Override
    protected Object execute(List<Object> args) {
        // For now, return the same as globals() since we don't have proper scope tracking
        // In a full implementation, this would return the actual local namespace
        Map<String, Object> locals = new HashMap<>();
        
        // Add some basic Python locals (same as globals at module level)
        locals.put("__name__", "__main__");
        locals.put("__doc__", null);
        locals.put("__package__", null);
        locals.put("__loader__", null);
        locals.put("__spec__", null);
        locals.put("__builtins__", au.com.allianceautomation.iython.builtins.BuiltinsModule.getInstance());
        
        return locals;
    }
}
