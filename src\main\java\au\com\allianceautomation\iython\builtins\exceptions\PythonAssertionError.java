package au.com.allianceautomation.iython.builtins.exceptions;

import java.util.List;

/**
 * Raised when an assert statement fails.
 * This corresponds to Python's AssertionError.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class PythonAssertionError extends PythonException {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * Constructs a new PythonAssertionError with no arguments.
     */
    public PythonAssertionError() {
        super();
    }
    
    /**
     * Constructs a new PythonAssertionError with the specified message.
     * 
     * @param message the detail message
     */
    public PythonAssertionError(String message) {
        super(message);
    }
    
    /**
     * Constructs a new PythonAssertionError with the specified arguments.
     * 
     * @param args the exception arguments
     */
    public PythonAssertionError(Object... args) {
        super(args);
    }
    
    /**
     * Constructs a new PythonAssertionError with the specified arguments.
     * 
     * @param args the exception arguments
     */
    public PythonAssertionError(List<Object> args) {
        super(args);
    }
    
    @Override
    public String getPythonClassName() {
        return "AssertionError";
    }
}
