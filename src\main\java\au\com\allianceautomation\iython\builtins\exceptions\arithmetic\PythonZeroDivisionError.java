package au.com.allianceautomation.iython.builtins.exceptions.arithmetic;

import java.util.List;

/**
 * Raised when the second argument of a division or modulo operation is zero.
 * This corresponds to Python's ZeroDivisionError.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class PythonZeroDivisionError extends PythonArithmeticError {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * Constructs a new PythonZeroDivisionError with no arguments.
     */
    public PythonZeroDivisionError() {
        super();
    }
    
    /**
     * Constructs a new PythonZeroDivisionError with the specified message.
     * 
     * @param message the detail message
     */
    public PythonZeroDivisionError(String message) {
        super(message);
    }
    
    /**
     * Constructs a new PythonZeroDivisionError with the specified arguments.
     * 
     * @param args the exception arguments
     */
    public PythonZeroDivisionError(Object... args) {
        super(args);
    }
    
    /**
     * Constructs a new PythonZeroDivisionError with the specified arguments.
     * 
     * @param args the exception arguments
     */
    public PythonZeroDivisionError(List<Object> args) {
        super(args);
    }
    
    @Override
    public String getPythonClassName() {
        return "ZeroDivisionError";
    }
}
