package au.com.allianceautomation.iython.builtins.functions;

import au.com.allianceautomation.iython.builtins.AbstractBuiltinFunction;
import java.util.List;
import java.util.HashMap;
import java.util.Map;

/**
 * Python object() builtin function.
 * 
 * Returns a new featureless object. object is a base for all classes.
 * It has the methods that are common to all instances of Python classes.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class ObjectFunction extends AbstractBuiltinFunction {
    
    public ObjectFunction() {
        super("object", 0, 0, "object() -> object");
    }
    
    @Override
    protected Object execute(List<Object> args) {
        return new PythonObject();
    }
    
    /**
     * Base object class for Python.
     */
    public static class PythonObject {
        private final Map<String, Object> attributes;
        private final int objectId;
        
        public PythonObject() {
            this.attributes = new HashMap<>();
            this.objectId = System.identityHashCode(this);
        }
        
        public Object getAttribute(String name) {
            if (attributes.containsKey(name)) {
                return attributes.get(name);
            }
            
            // Handle special attributes
            switch (name) {
                case "__class__":
                    return this.getClass();
                case "__dict__":
                    return new HashMap<>(attributes);
                case "__doc__":
                    return "The most base type";
                case "__module__":
                    return "builtins";
                default:
                    throw new RuntimeException("AttributeError: '" + getClass().getSimpleName() + 
                                             "' object has no attribute '" + name + "'");
            }
        }
        
        public void setAttribute(String name, Object value) {
            attributes.put(name, value);
        }
        
        public boolean hasAttribute(String name) {
            return attributes.containsKey(name) || 
                   name.equals("__class__") || name.equals("__dict__") || 
                   name.equals("__doc__") || name.equals("__module__");
        }
        
        public void deleteAttribute(String name) {
            if (attributes.containsKey(name)) {
                attributes.remove(name);
            } else {
                throw new RuntimeException("AttributeError: '" + getClass().getSimpleName() + 
                                         "' object has no attribute '" + name + "'");
            }
        }
        
        public String pythonStr() {
            return toString();
        }
        
        public String pythonRepr() {
            return "<object object at 0x" + Integer.toHexString(objectId) + ">";
        }
        
        public boolean pythonBool() {
            return true; // Objects are truthy by default
        }
        
        public int pythonHash() {
            return objectId;
        }
        
        public boolean pythonEquals(Object other) {
            return this == other; // Identity comparison by default
        }
        
        @Override
        public String toString() {
            return pythonRepr();
        }
        
        @Override
        public boolean equals(Object obj) {
            return pythonEquals(obj);
        }
        
        @Override
        public int hashCode() {
            return pythonHash();
        }
    }
}
