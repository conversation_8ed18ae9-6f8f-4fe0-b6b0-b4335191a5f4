package au.com.allianceautomation.iython;

import java.util.List;
import java.util.Map;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

/**
 * Test class for dictionary creation and comprehensions.
 */
public class DictAndComprehensionTest {
    
    private PythonExecutor executor;
    
    @BeforeEach
    void setUp() {
        executor = new PythonExecutor();
    }
    
    @Test
    @DisplayName("Test simple dictionary creation")
    void testSimpleDictionary() throws PythonExecutionException {
        String code = "d = {'a': 1, 'b': 2, 'c': 3}";
        executor.executeCode(code);

        Object result = executor.executeAndGetVariable("print(d)", "d");
        assertTrue(result instanceof Map);
        Map<?, ?> dict = (Map<?, ?>) result;
        assertEquals(3, dict.size());
        assertEquals(1L, dict.get("a"));
        assertEquals(2L, dict.get("b"));
        assertEquals(3L, dict.get("c"));
    }
    
    @Test
    @DisplayName("Test empty dictionary creation")
    void testEmptyDictionary() throws PythonExecutionException {
        String code = "d = {}";
        executor.executeCode(code);

        Object result = executor.executeAndGetVariable("print(d)", "d");
        assertTrue(result instanceof Map);
        Map<?, ?> dict = (Map<?, ?>) result;
        assertEquals(0, dict.size());
    }
    
    @Test
    @DisplayName("Test simple set creation")
    void testSimpleSet() throws PythonExecutionException {
        String code = "s = {1, 2, 3, 4, 5}";
        executor.executeCode(code);

        Object result = executor.executeAndGetVariable("print(s)", "s");
        assertTrue(result instanceof Set);
        Set<?> set = (Set<?>) result;
        assertEquals(5, set.size());
        assertTrue(set.contains(1L));
        assertTrue(set.contains(2L));
        assertTrue(set.contains(3L));
        assertTrue(set.contains(4L));
        assertTrue(set.contains(5L));
    }
    
    @Test
    @DisplayName("Test list comprehension")
    void testListComprehension() throws PythonExecutionException {
        String code = "numbers = [1, 2, 3, 4, 5]\n" +
                      "squared = [x * x for x in numbers]";
        executor.executeCode(code);

        Object result = executor.executeAndGetVariable("print(squared)", "squared");
        assertTrue(result instanceof List);
        List<?> list = (List<?>) result;
        assertEquals(5, list.size());
        // Handle both Long and Double types (arithmetic operations may return Double)
        assertEquals(1.0, ((Number) list.get(0)).doubleValue(), 0.001);
        assertEquals(4.0, ((Number) list.get(1)).doubleValue(), 0.001);
        assertEquals(9.0, ((Number) list.get(2)).doubleValue(), 0.001);
        assertEquals(16.0, ((Number) list.get(3)).doubleValue(), 0.001);
        assertEquals(25.0, ((Number) list.get(4)).doubleValue(), 0.001);
    }
    
    @Test
    @DisplayName("Test list comprehension with condition")
    void testListComprehensionWithCondition() throws PythonExecutionException {
        String code = "numbers = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]\n" +
                      "evens = [x for x in numbers if x % 2 == 0]";
        executor.executeCode(code);

        Object result = executor.executeAndGetVariable("print(evens)", "evens");
        assertTrue(result instanceof List);
        List<?> list = (List<?>) result;
        assertEquals(5, list.size());
        assertEquals(2.0, ((Number) list.get(0)).doubleValue(), 0.001);
        assertEquals(4.0, ((Number) list.get(1)).doubleValue(), 0.001);
        assertEquals(6.0, ((Number) list.get(2)).doubleValue(), 0.001);
        assertEquals(8.0, ((Number) list.get(3)).doubleValue(), 0.001);
        assertEquals(10.0, ((Number) list.get(4)).doubleValue(), 0.001);
    }
    
    @Test
    @DisplayName("Test dictionary comprehension")
    void testDictComprehension() throws PythonExecutionException {
        String code = "numbers = [1, 2, 3, 4, 5]\n" +
                      "squared_dict = {x: x * x for x in numbers}";
        executor.executeCode(code);

        Object result = executor.executeAndGetVariable("print(squared_dict)", "squared_dict");
        assertTrue(result instanceof Map);
        Map<?, ?> dict = (Map<?, ?>) result;
        assertEquals(5, dict.size());

        // Keys are Long, values are Double
        assertEquals(1.0, ((Number) dict.get(1L)).doubleValue(), 0.001);
        assertEquals(4.0, ((Number) dict.get(2L)).doubleValue(), 0.001);
        assertEquals(9.0, ((Number) dict.get(3L)).doubleValue(), 0.001);
        assertEquals(16.0, ((Number) dict.get(4L)).doubleValue(), 0.001);
        assertEquals(25.0, ((Number) dict.get(5L)).doubleValue(), 0.001);
    }
    
    @Test
    @DisplayName("Test set comprehension")
    void testSetComprehension() throws PythonExecutionException {
        String code = "numbers = [1, 2, 3, 4, 5, 1, 2, 3]\n" +
                      "unique_squares = {x * x for x in numbers}";
        executor.executeCode(code);

        Object result = executor.executeAndGetVariable("print(unique_squares)", "unique_squares");
        assertTrue(result instanceof Set);
        Set<?> set = (Set<?>) result;
        assertEquals(5, set.size());
        // Values may be Double instead of Long
        assertTrue(set.contains(1.0));
        assertTrue(set.contains(4.0));
        assertTrue(set.contains(9.0));
        assertTrue(set.contains(16.0));
        assertTrue(set.contains(25.0));
    }
}
