package au.com.allianceautomation.iython.runtime.exceptions;

import au.com.allianceautomation.iython.runtime.traceback.PythonTraceback;

/**
 * Enhanced runtime exception that includes Python traceback information.
 * This exception is thrown during Python code execution and provides
 * detailed traceback information similar to CPython.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class PythonRuntimeException extends RuntimeException {
    
    private static final long serialVersionUID = 1L;
    
    private final String pythonExceptionType;
    private final PythonTraceback traceback;
    private final String suggestion;
    
    /**
     * Creates a new PythonRuntimeException.
     * 
     * @param pythonExceptionType the Python exception type (e.g., "NameError", "TypeError")
     * @param message the exception message
     * @param traceback the Python traceback
     */
    public PythonRuntimeException(String pythonExceptionType, String message, PythonTraceback traceback) {
        this(pythonExceptionType, message, traceback, null);
    }
    
    /**
     * Creates a new PythonRuntimeException with a suggestion.
     * 
     * @param pythonExceptionType the Python exception type (e.g., "NameError", "TypeError")
     * @param message the exception message
     * @param traceback the Python traceback
     * @param suggestion optional suggestion for fixing the error
     */
    public PythonRuntimeException(String pythonExceptionType, String message, PythonTraceback traceback, String suggestion) {
        super(message);
        this.pythonExceptionType = pythonExceptionType;
        this.traceback = traceback != null ? traceback : new PythonTraceback();
        this.suggestion = suggestion;
    }
    
    /**
     * Gets the Python exception type.
     * 
     * @return the exception type
     */
    public String getPythonExceptionType() {
        return pythonExceptionType;
    }
    
    /**
     * Gets the traceback information.
     * 
     * @return the traceback
     */
    public PythonTraceback getTraceback() {
        return traceback;
    }
    
    /**
     * Gets the suggestion for fixing the error.
     * 
     * @return the suggestion, or null if none available
     */
    public String getSuggestion() {
        return suggestion;
    }
    
    /**
     * Formats the exception in CPython style.
     * 
     * @return formatted exception string
     */
    public String formatPythonException() {
        StringBuilder sb = new StringBuilder();
        
        // Add traceback if present
        if (!traceback.isEmpty()) {
            sb.append(traceback.format());
        }
        
        // Add exception type and message
        sb.append(pythonExceptionType);
        if (getMessage() != null && !getMessage().isEmpty()) {
            sb.append(": ").append(getMessage());
        }
        
        // Add suggestion if available
        if (suggestion != null && !suggestion.isEmpty()) {
            sb.append(". Did you mean: '").append(suggestion).append("'?");
        }
        
        return sb.toString();
    }
    
    @Override
    public String toString() {
        return formatPythonException();
    }
    
    /**
     * Creates a NameError with suggestion.
     * 
     * @param undefinedName the undefined name
     * @param traceback the traceback
     * @param suggestion optional suggestion
     * @return new PythonRuntimeException
     */
    public static PythonRuntimeException nameError(String undefinedName, PythonTraceback traceback, String suggestion) {
        String message = "name '" + undefinedName + "' is not defined";
        return new PythonRuntimeException("NameError", message, traceback, suggestion);
    }
    
    /**
     * Creates a TypeError.
     * 
     * @param message the error message
     * @param traceback the traceback
     * @return new PythonRuntimeException
     */
    public static PythonRuntimeException typeError(String message, PythonTraceback traceback) {
        return new PythonRuntimeException("TypeError", message, traceback);
    }
    
    /**
     * Creates a ValueError.
     * 
     * @param message the error message
     * @param traceback the traceback
     * @return new PythonRuntimeException
     */
    public static PythonRuntimeException valueError(String message, PythonTraceback traceback) {
        return new PythonRuntimeException("ValueError", message, traceback);
    }
    
    /**
     * Creates an AttributeError.
     * 
     * @param message the error message
     * @param traceback the traceback
     * @return new PythonRuntimeException
     */
    public static PythonRuntimeException attributeError(String message, PythonTraceback traceback) {
        return new PythonRuntimeException("AttributeError", message, traceback);
    }
    
    /**
     * Creates a SyntaxError.
     * 
     * @param message the error message
     * @param traceback the traceback
     * @return new PythonRuntimeException
     */
    public static PythonRuntimeException syntaxError(String message, PythonTraceback traceback) {
        return new PythonRuntimeException("SyntaxError", message, traceback);
    }
}
