package au.com.allianceautomation.iython.runtime.traceback;

import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Utility class for suggesting similar names when a NameError occurs.
 * Implements fuzzy string matching to provide "Did you mean?" suggestions.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class NameSuggester {
    
    private static final int MAX_SUGGESTIONS = 3;
    private static final double SIMILARITY_THRESHOLD = 0.5;
    
    /**
     * Suggests similar names for a given undefined name.
     * 
     * @param undefinedName the name that was not found
     * @param availableNames collection of available names to search
     * @return the best suggestion, or null if no good match found
     */
    public static String suggestSimilarName(String undefinedName, Collection<String> availableNames) {
        if (undefinedName == null || undefinedName.isEmpty() || availableNames == null) {
            return null;
        }
        
        List<NameMatch> matches = availableNames.stream()
            .map(name -> new NameMatch(name, calculateSimilarity(undefinedName, name)))
            .filter(match -> match.similarity >= SIMILARITY_THRESHOLD)
            .sorted((a, b) -> Double.compare(b.similarity, a.similarity))
            .limit(MAX_SUGGESTIONS)
            .collect(Collectors.toList());
        
        return matches.isEmpty() ? null : matches.get(0).name;
    }
    
    /**
     * Calculates similarity between two strings using a combination of techniques.
     * 
     * @param s1 first string
     * @param s2 second string
     * @return similarity score between 0.0 and 1.0
     */
    private static double calculateSimilarity(String s1, String s2) {
        if (s1.equals(s2)) {
            return 1.0;
        }
        
        // Use a combination of different similarity measures
        double levenshteinSim = 1.0 - (double) levenshteinDistance(s1, s2) / Math.max(s1.length(), s2.length());
        double jaccardSim = jaccardSimilarity(s1, s2);
        double prefixSim = commonPrefixSimilarity(s1, s2);
        
        // Weight the different measures
        return 0.5 * levenshteinSim + 0.3 * jaccardSim + 0.2 * prefixSim;
    }
    
    /**
     * Calculates Levenshtein distance between two strings.
     * 
     * @param s1 first string
     * @param s2 second string
     * @return edit distance
     */
    private static int levenshteinDistance(String s1, String s2) {
        int[][] dp = new int[s1.length() + 1][s2.length() + 1];
        
        for (int i = 0; i <= s1.length(); i++) {
            dp[i][0] = i;
        }
        for (int j = 0; j <= s2.length(); j++) {
            dp[0][j] = j;
        }
        
        for (int i = 1; i <= s1.length(); i++) {
            for (int j = 1; j <= s2.length(); j++) {
                if (s1.charAt(i - 1) == s2.charAt(j - 1)) {
                    dp[i][j] = dp[i - 1][j - 1];
                } else {
                    dp[i][j] = 1 + Math.min(Math.min(dp[i - 1][j], dp[i][j - 1]), dp[i - 1][j - 1]);
                }
            }
        }
        
        return dp[s1.length()][s2.length()];
    }
    
    /**
     * Calculates Jaccard similarity based on character bigrams.
     * 
     * @param s1 first string
     * @param s2 second string
     * @return Jaccard similarity
     */
    private static double jaccardSimilarity(String s1, String s2) {
        if (s1.length() < 2 || s2.length() < 2) {
            return s1.equals(s2) ? 1.0 : 0.0;
        }
        
        java.util.Set<String> bigrams1 = getBigrams(s1);
        java.util.Set<String> bigrams2 = getBigrams(s2);
        
        java.util.Set<String> intersection = new java.util.HashSet<>(bigrams1);
        intersection.retainAll(bigrams2);
        
        java.util.Set<String> union = new java.util.HashSet<>(bigrams1);
        union.addAll(bigrams2);
        
        return union.isEmpty() ? 0.0 : (double) intersection.size() / union.size();
    }
    
    /**
     * Gets character bigrams from a string.
     * 
     * @param s the string
     * @return set of bigrams
     */
    private static java.util.Set<String> getBigrams(String s) {
        java.util.Set<String> bigrams = new java.util.HashSet<>();
        for (int i = 0; i < s.length() - 1; i++) {
            bigrams.add(s.substring(i, i + 2));
        }
        return bigrams;
    }
    
    /**
     * Calculates similarity based on common prefix length.
     * 
     * @param s1 first string
     * @param s2 second string
     * @return prefix similarity
     */
    private static double commonPrefixSimilarity(String s1, String s2) {
        int commonLength = 0;
        int minLength = Math.min(s1.length(), s2.length());
        
        for (int i = 0; i < minLength; i++) {
            if (s1.charAt(i) == s2.charAt(i)) {
                commonLength++;
            } else {
                break;
            }
        }
        
        return (double) commonLength / Math.max(s1.length(), s2.length());
    }
    
    /**
     * Helper class to store name and similarity score.
     */
    private static class NameMatch {
        final String name;
        final double similarity;
        
        NameMatch(String name, double similarity) {
            this.name = name;
            this.similarity = similarity;
        }
    }
}
