package au.com.allianceautomation.iython.builtins.functions;

import au.com.allianceautomation.iython.builtins.AbstractBuiltinFunction;
import java.util.List;
import java.util.Arrays;
import java.nio.charset.StandardCharsets;

/**
 * Python bytearray() builtin function.
 * 
 * Returns a mutable bytearray object constructed from:
 * - bytearray() -> empty bytearray object
 * - bytearray(int) -> zero-filled bytearray object of given length
 * - bytearray(iterable_of_ints) -> bytearray object from sequence of integers 0-255
 * - bytearray(string, encoding) -> bytearray object from string using encoding
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class ByteArrayFunction extends AbstractBuiltinFunction {
    
    public ByteArrayFunction() {
        super("bytearray", 0, 3, "bytearray([source[, encoding[, errors]]]) -> bytearray");
    }
    
    @Override
    protected Object execute(List<Object> args) {
        if (args.isEmpty()) {
            // bytearray() -> empty bytearray
            return new PythonByteArray(new byte[0]);
        }
        
        Object source = args.get(0);
        
        if (source instanceof Integer) {
            // bytearray(int) -> zero-filled bytearray of given length
            int length = (Integer) source;
            if (length < 0) {
                throw new RuntimeException("ValueError: negative count");
            }
            return new PythonByteArray(new byte[length]);
        } else if (source instanceof String) {
            // bytearray(string, encoding)
            String str = (String) source;
            String encoding = args.size() > 1 ? pythonStr(args.get(1)) : "utf-8";
            
            try {
                byte[] bytes;
                switch (encoding.toLowerCase()) {
                    case "utf-8":
                    case "utf8":
                        bytes = str.getBytes(StandardCharsets.UTF_8);
                        break;
                    case "ascii":
                        bytes = str.getBytes(StandardCharsets.US_ASCII);
                        break;
                    case "latin-1":
                    case "iso-8859-1":
                        bytes = str.getBytes(StandardCharsets.ISO_8859_1);
                        break;
                    default:
                        throw new RuntimeException("LookupError: unknown encoding: " + encoding);
                }
                return new PythonByteArray(bytes);
            } catch (Exception e) {
                throw new RuntimeException("UnicodeEncodeError: " + e.getMessage());
            }
        } else if (source instanceof List) {
            // bytearray(iterable_of_ints)
            List<?> list = (List<?>) source;
            byte[] bytes = new byte[list.size()];
            for (int i = 0; i < list.size(); i++) {
                Object item = list.get(i);
                if (!(item instanceof Integer)) {
                    throw new RuntimeException("TypeError: an integer is required");
                }
                int value = (Integer) item;
                if (value < 0 || value > 255) {
                    throw new RuntimeException("ValueError: bytes must be in range(0, 256)");
                }
                bytes[i] = (byte) value;
            }
            return new PythonByteArray(bytes);
        } else if (source.getClass().isArray()) {
            // Handle arrays
            Object[] array = (Object[]) source;
            byte[] bytes = new byte[array.length];
            for (int i = 0; i < array.length; i++) {
                if (!(array[i] instanceof Integer)) {
                    throw new RuntimeException("TypeError: an integer is required");
                }
                int value = (Integer) array[i];
                if (value < 0 || value > 255) {
                    throw new RuntimeException("ValueError: bytes must be in range(0, 256)");
                }
                bytes[i] = (byte) value;
            }
            return new PythonByteArray(bytes);
        } else {
            throw new RuntimeException("TypeError: cannot convert '" + 
                                     source.getClass().getSimpleName() + "' object to bytearray");
        }
    }
    
    /**
     * Mutable bytearray object for Python.
     */
    public static class PythonByteArray {
        private byte[] data;
        
        public PythonByteArray(byte[] data) {
            this.data = data.clone();
        }
        
        public byte[] getData() {
            return data.clone();
        }
        
        public int length() {
            return data.length;
        }
        
        public byte get(int index) {
            if (index < 0 || index >= data.length) {
                throw new RuntimeException("IndexError: index out of range");
            }
            return data[index];
        }
        
        public void set(int index, int value) {
            if (index < 0 || index >= data.length) {
                throw new RuntimeException("IndexError: index out of range");
            }
            if (value < 0 || value > 255) {
                throw new RuntimeException("ValueError: byte must be in range(0, 256)");
            }
            data[index] = (byte) value;
        }
        
        public void append(int value) {
            if (value < 0 || value > 255) {
                throw new RuntimeException("ValueError: byte must be in range(0, 256)");
            }
            byte[] newData = new byte[data.length + 1];
            System.arraycopy(data, 0, newData, 0, data.length);
            newData[data.length] = (byte) value;
            data = newData;
        }
        
        @Override
        public String toString() {
            StringBuilder sb = new StringBuilder("bytearray(b'");
            for (byte b : data) {
                int value = b & 0xFF;
                if (value >= 32 && value <= 126 && value != 39 && value != 92) {
                    // Printable ASCII (except single quote and backslash)
                    sb.append((char) value);
                } else {
                    sb.append(String.format("\\x%02x", value));
                }
            }
            sb.append("')");
            return sb.toString();
        }
        
        @Override
        public boolean equals(Object obj) {
            if (this == obj) return true;
            if (!(obj instanceof PythonByteArray)) return false;
            PythonByteArray other = (PythonByteArray) obj;
            return Arrays.equals(data, other.data);
        }
        
        @Override
        public int hashCode() {
            return Arrays.hashCode(data);
        }
    }
}
