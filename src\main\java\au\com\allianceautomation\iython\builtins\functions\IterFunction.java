package au.com.allianceautomation.iython.builtins.functions;

import au.com.allianceautomation.iython.builtins.AbstractBuiltinFunction;
import java.util.List;
import java.util.ArrayList;
import java.util.Iterator;

/**
 * Python iter() builtin function.
 * 
 * Returns an iterator object. The first argument is interpreted very differently 
 * depending on the presence of the second argument.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class IterFunction extends AbstractBuiltinFunction {
    
    public IterFunction() {
        super("iter", 1, 2, "iter(iterable) -> iterator or iter(callable, sentinel) -> iterator");
    }
    
    @Override
    protected Object execute(List<Object> args) {
        if (args.size() == 1) {
            // iter(iterable) -> iterator
            Object iterable = args.get(0);
            return createIterator(iterable);
        } else {
            // iter(callable, sentinel) -> iterator
            Object callable = args.get(0);
            Object sentinel = args.get(1);
            
            // This would require implementing a callable iterator
            // For now, throw an exception
            throw new RuntimeException("iter(callable, sentinel) not yet implemented");
        }
    }
    
    private Iterator<Object> createIterator(Object iterable) {
        if (iterable instanceof List) {
            List<?> list = (List<?>) iterable;
            List<Object> copy = new ArrayList<>();
            for (Object item : list) {
                copy.add(item);
            }
            return copy.iterator();
        } else if (iterable instanceof String) {
            String str = (String) iterable;
            List<Object> chars = new ArrayList<>();
            for (char c : str.toCharArray()) {
                chars.add(String.valueOf(c));
            }
            return chars.iterator();
        } else if (iterable.getClass().isArray()) {
            Object[] array = (Object[]) iterable;
            List<Object> list = new ArrayList<>();
            for (Object item : array) {
                list.add(item);
            }
            return list.iterator();
        } else if (iterable instanceof Iterator) {
            // Already an iterator
            return (Iterator<Object>) iterable;
        } else {
            throw new RuntimeException("'" + iterable.getClass().getSimpleName() + "' object is not iterable");
        }
    }
}
