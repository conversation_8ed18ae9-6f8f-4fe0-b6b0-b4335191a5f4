package au.com.allianceautomation.iython.runtime;

import java.io.PrintStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import au.com.allianceautomation.iython.ast.ASTVisitor;
import au.com.allianceautomation.iython.ast.Program;
import au.com.allianceautomation.iython.ast.expressions.AttributeExpression;
import au.com.allianceautomation.iython.ast.expressions.BinaryOpExpression;
import au.com.allianceautomation.iython.ast.expressions.BoolOpExpression;
import au.com.allianceautomation.iython.ast.expressions.CallExpression;
import au.com.allianceautomation.iython.ast.expressions.CompareExpression;
import au.com.allianceautomation.iython.ast.expressions.ConditionalExpression;
import au.com.allianceautomation.iython.ast.expressions.DictExpression;
import au.com.allianceautomation.iython.ast.expressions.Expression;
import au.com.allianceautomation.iython.ast.expressions.ListExpression;
import au.com.allianceautomation.iython.ast.expressions.LiteralExpression;
import au.com.allianceautomation.iython.ast.expressions.NameExpression;
import au.com.allianceautomation.iython.ast.expressions.SubscriptExpression;
import au.com.allianceautomation.iython.ast.expressions.UnaryOpExpression;
import au.com.allianceautomation.iython.ast.statements.AssertStatement;
import au.com.allianceautomation.iython.ast.statements.AssignmentStatement;
import au.com.allianceautomation.iython.ast.statements.ClassDefStatement;
import au.com.allianceautomation.iython.ast.statements.DeleteStatement;
import au.com.allianceautomation.iython.ast.statements.ExpressionStatement;
import au.com.allianceautomation.iython.ast.statements.ForStatement;
import au.com.allianceautomation.iython.ast.statements.FunctionDefStatement;
import au.com.allianceautomation.iython.ast.statements.GlobalStatement;
import au.com.allianceautomation.iython.ast.statements.IfStatement;
import au.com.allianceautomation.iython.ast.statements.ImportStatement;
import au.com.allianceautomation.iython.ast.statements.PassStatement;
import au.com.allianceautomation.iython.ast.statements.ReturnStatement;
import au.com.allianceautomation.iython.ast.statements.Statement;
import au.com.allianceautomation.iython.ast.statements.WhileStatement;
import au.com.allianceautomation.iython.builtins.BuiltinFunction;
import au.com.allianceautomation.iython.builtins.BuiltinRegistry;
import au.com.allianceautomation.iython.builtins.ExceptionRegistry;
import au.com.allianceautomation.iython.runtime.exceptions.PythonRuntimeException;
import au.com.allianceautomation.iython.runtime.traceback.NameSuggester;
import au.com.allianceautomation.iython.runtime.traceback.PythonTraceback;

/**
 * Runtime system for executing Python AST nodes.
 * This class implements the visitor pattern to traverse and execute AST nodes.
 *
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class PythonRuntime implements ASTVisitor<Object> {

    private static final Logger logger = LoggerFactory.getLogger(PythonRuntime.class);

    private Map<String, Object> variables;
    private PrintStream output;
    private StringBuilder outputBuffer;
    private Object lastExpressionResult;
    private PythonTraceback currentTraceback;
    private String currentFilename;

    public PythonRuntime() {
        this.outputBuffer = new StringBuilder();
    }

    /**
     * Execute a Python program.
     *
     * @param program The program to execute
     * @param globalVars Global variables to use
     * @param outputStream Output stream for print statements
     * @return The captured output
     */
    public String execute(Program program, Map<String, Object> globalVars, PrintStream outputStream) {
        this.variables = new HashMap<>(globalVars);
        this.output = outputStream;
        this.outputBuffer = new StringBuilder();
        this.lastExpressionResult = null;
        this.currentTraceback = new PythonTraceback();
        this.currentFilename = "<stdin>"; // Default for interactive mode

        try {
            program.accept(this);

            // If we have a single expression statement that returned a non-None value,
            // display it (this mimics Python REPL behavior)
            if (lastExpressionResult != null && program.getStatements().size() == 1 &&
                program.getStatements().get(0) instanceof au.com.allianceautomation.iython.ast.statements.ExpressionStatement) {
                String repr = formatForDisplay(lastExpressionResult);
                outputBuffer.append(repr).append("\n");
                if (this.output != null) {
                    this.output.println(repr);
                }
            }

            // Update global variables
            globalVars.putAll(variables);

            return outputBuffer.toString();
        } catch (PythonRuntimeException e) {
            // Re-throw Python runtime exceptions as-is for proper formatting
            throw e;
        } catch (Exception e) {
            logger.error("Runtime error during execution", e);
            throw new RuntimeException("Python execution failed", e);
        }
    }

    // Program visitor
    @Override
    public Object visitProgram(Program node) {
        for (Statement stmt : node.getStatements()) {
            stmt.accept(this);
        }
        return null;
    }

    // Statement visitors
    @Override
    public Object visitExpressionStmt(ExpressionStatement node) {
        Object result = node.getExpression().accept(this);
        // Store the result for potential display in REPL
        this.lastExpressionResult = result;
        return result;
    }

    @Override
    public Object visitAssignment(AssignmentStatement node) {
        Object value = node.getValue().accept(this);

        if (node.getTarget() instanceof NameExpression) {
            String varName = ((NameExpression) node.getTarget()).getName();
            variables.put(varName, value);
        }

        return value;
    }

    @Override
    public Object visitIf(IfStatement node) {
        // Placeholder implementation
        return null;
    }

    @Override
    public Object visitWhile(WhileStatement node) {
        // Placeholder implementation
        return null;
    }

    @Override
    public Object visitFor(ForStatement node) {
        // Placeholder implementation
        return null;
    }

    @Override
    public Object visitFunctionDef(FunctionDefStatement node) {
        // Placeholder implementation
        return null;
    }

    @Override
    public Object visitClassDef(ClassDefStatement node) {
        // Placeholder implementation
        return null;
    }

    @Override
    public Object visitReturn(ReturnStatement node) {
        // Placeholder implementation
        return null;
    }

    @Override
    public Object visitPass(PassStatement node) {
        // Pass statement does nothing
        return null;
    }

    @Override
    public Object visitDelete(DeleteStatement node) {
        // Placeholder implementation
        return null;
    }

    @Override
    public Object visitImport(ImportStatement node) {
        // Placeholder implementation
        return null;
    }

    @Override
    public Object visitGlobal(GlobalStatement node) {
        // Placeholder implementation
        return null;
    }

    @Override
    public Object visitAssert(AssertStatement node) {
        // Placeholder implementation
        return null;
    }

    // Expression visitors
    @Override
    public Object visitLiteral(LiteralExpression node) {
        return node.getValue();
    }

    @Override
    public Object visitName(NameExpression node) {
        String name = node.getName();

        // Check for special Python variables
        if ("__builtins__".equals(name)) {
            return au.com.allianceautomation.iython.builtins.BuiltinsModule.getInstance();
        }

        // Check for built-in functions using the registry
        if (BuiltinRegistry.isBuiltin(name)) {
            return BuiltinRegistry.getBuiltin(name);
        }

        // Check for built-in exceptions using the exception registry
        if (ExceptionRegistry.isException(name)) {
            return ExceptionRegistry.getException(name);
        }

        // Look up variable
        Object value = variables.get(name);
        if (value == null) {
            // Create traceback frame for this error
            currentTraceback.addFrame(currentFilename, node.getLine(), "<module>",
                                    "# Line " + node.getLine() + " (name lookup)");

            // Try to suggest a similar name
            java.util.Set<String> availableNames = new java.util.HashSet<>();
            availableNames.addAll(variables.keySet());
            availableNames.addAll(BuiltinRegistry.getBuiltinNames());

            String suggestion = NameSuggester.suggestSimilarName(name, availableNames);

            throw PythonRuntimeException.nameError(name, currentTraceback, suggestion);
        }

        return value;
    }

    @Override
    public Object visitBinaryOp(BinaryOpExpression node) {
        Object left = node.getLeft().accept(this);
        Object right = node.getRight().accept(this);

        switch (node.getOperator()) {
            case ADD:
                return performAddition(left, right);
            case SUBTRACT:
                return performSubtraction(left, right);
            case MULTIPLY:
                return performMultiplication(left, right);
            case DIVIDE:
                return performDivision(left, right);
            case EQUAL:
                return performEquals(left, right);
            case NOT_EQUAL:
                return !performEquals(left, right);
            case LESS_THAN:
                return performLessThan(left, right);
            case GREATER_THAN:
                return performGreaterThan(left, right);
            default:
                throw new RuntimeException("Unsupported binary operator: " + node.getOperator());
        }
    }

    @Override
    public Object visitUnaryOp(UnaryOpExpression node) {
        Object operand = node.getOperand().accept(this);

        switch (node.getOperator()) {
            case PLUS:
                return operand; // Unary plus
            case MINUS:
                if (operand instanceof Number) {
                    if (operand instanceof Integer) {
                        return -(Integer) operand;
                    } else if (operand instanceof Double) {
                        return -(Double) operand;
                    }
                }
                throw new RuntimeException("Unsupported unary minus for type: " + operand.getClass());
            case NOT:
                return !isTruthy(operand);
            default:
                throw new RuntimeException("Unsupported unary operator: " + node.getOperator());
        }
    }

    @Override
    public Object visitCall(CallExpression node) {
        Object function = node.getFunction().accept(this);

        if (function instanceof BuiltinFunction) {
            BuiltinFunction builtin = (BuiltinFunction) function;

            // Evaluate arguments
            List<Object> args = new ArrayList<>();
            for (Expression argExpr : node.getArguments()) {
                args.add(argExpr.accept(this));
            }

            // Special handling for print function to manage output
            if ("print".equals(builtin.getName())) {
                String printResult = (String) builtin.call(args);
                outputBuffer.append(printResult);
                if (this.output != null) {
                    this.output.print(printResult);
                }
                return null; // print returns None
            } else {
                // Call the builtin function
                return builtin.call(args);
            }
        }

        throw new RuntimeException("Object is not callable: " + function);
    }

    @Override
    public Object visitAttribute(AttributeExpression node) {
        // Placeholder implementation
        return null;
    }

    @Override
    public Object visitSubscript(SubscriptExpression node) {
        // Placeholder implementation
        return null;
    }

    @Override
    public Object visitList(ListExpression node) {
        List<Object> elements = new ArrayList<>();
        for (Expression element : node.getElements()) {
            elements.add(element.accept(this));
        }
        return elements;
    }

    @Override
    public Object visitDict(DictExpression node) {
        // Placeholder implementation
        return null;
    }

    @Override
    public Object visitCompare(CompareExpression node) {
        // Placeholder implementation
        return null;
    }

    @Override
    public Object visitBoolOp(BoolOpExpression node) {
        // Placeholder implementation
        return null;
    }

    @Override
    public Object visitConditional(ConditionalExpression node) {
        Object test = node.getTest().accept(this);
        if (isTruthy(test)) {
            return node.getBody().accept(this);
        } else {
            return node.getOrelse().accept(this);
        }
    }

    // Helper methods
    private Object performAddition(Object left, Object right) {
        if (left instanceof Number && right instanceof Number) {
            if (left instanceof Integer && right instanceof Integer) {
                return (Integer) left + (Integer) right;
            } else {
                return ((Number) left).doubleValue() + ((Number) right).doubleValue();
            }
        } else if (left instanceof String || right instanceof String) {
            return pythonStr(left) + pythonStr(right);
        }
        throw new RuntimeException("Unsupported operand types for +: " + left.getClass() + " and " + right.getClass());
    }

    private Object performSubtraction(Object left, Object right) {
        if (left instanceof Number && right instanceof Number) {
            if (left instanceof Integer && right instanceof Integer) {
                return (Integer) left - (Integer) right;
            } else {
                return ((Number) left).doubleValue() - ((Number) right).doubleValue();
            }
        }
        throw new RuntimeException("Unsupported operand types for -: " + left.getClass() + " and " + right.getClass());
    }

    private Object performMultiplication(Object left, Object right) {
        if (left instanceof Number && right instanceof Number) {
            if (left instanceof Integer && right instanceof Integer) {
                return (Integer) left * (Integer) right;
            } else {
                return ((Number) left).doubleValue() * ((Number) right).doubleValue();
            }
        }
        throw new RuntimeException("Unsupported operand types for *: " + left.getClass() + " and " + right.getClass());
    }

    private Object performDivision(Object left, Object right) {
        if (left instanceof Number && right instanceof Number) {
            return ((Number) left).doubleValue() / ((Number) right).doubleValue();
        }
        throw new RuntimeException("Unsupported operand types for /: " + left.getClass() + " and " + right.getClass());
    }

    private boolean performEquals(Object left, Object right) {
        if (left == null && right == null) return true;
        if (left == null || right == null) return false;
        return left.equals(right);
    }

    private boolean performLessThan(Object left, Object right) {
        if (left instanceof Number && right instanceof Number) {
            return ((Number) left).doubleValue() < ((Number) right).doubleValue();
        }
        throw new RuntimeException("Unsupported operand types for <: " + left.getClass() + " and " + right.getClass());
    }

    private boolean performGreaterThan(Object left, Object right) {
        if (left instanceof Number && right instanceof Number) {
            return ((Number) left).doubleValue() > ((Number) right).doubleValue();
        }
        throw new RuntimeException("Unsupported operand types for >: " + left.getClass() + " and " + right.getClass());
    }

    private boolean isTruthy(Object obj) {
        if (obj == null) return false;
        if (obj instanceof Boolean) return (Boolean) obj;
        if (obj instanceof Number) return ((Number) obj).doubleValue() != 0.0;
        if (obj instanceof String) return !((String) obj).isEmpty();
        return true; // Most objects are truthy
    }

    private String pythonStr(Object obj) {
        if (obj == null) return "None";
        if (obj instanceof String) return (String) obj;
        return obj.toString();
    }

    /**
     * Format an object for display in the REPL.
     * This mimics Python's repr() function behavior.
     */
    private String formatForDisplay(Object obj) {
        if (obj == null) {
            return "None";
        }

        if (obj instanceof String) {
            // For strings, show them with quotes like Python repr()
            return "'" + obj.toString().replace("'", "\\'") + "'";
        }

        if (obj instanceof List) {
            // Format lists like Python
            List<?> list = (List<?>) obj;
            StringBuilder sb = new StringBuilder("[");
            for (int i = 0; i < list.size(); i++) {
                if (i > 0) sb.append(", ");
                sb.append(formatForDisplay(list.get(i)));
            }
            sb.append("]");
            return sb.toString();
        }

        if (obj instanceof Boolean) {
            // Python uses True/False, not true/false
            return ((Boolean) obj) ? "True" : "False";
        }

        // For other objects, use their toString representation
        return obj.toString();
    }


}
