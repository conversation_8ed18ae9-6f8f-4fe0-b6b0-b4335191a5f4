package au.com.allianceautomation.iython.builtins.exceptions.lookup;

import au.com.allianceautomation.iython.builtins.exceptions.PythonException;
import java.util.List;

/**
 * Base class for the exceptions that are raised when a key or index used on a 
 * mapping or sequence is invalid.
 * This corresponds to Python's LookupError.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class PythonLookupError extends PythonException {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * Constructs a new PythonLookupError with no arguments.
     */
    public PythonLookupError() {
        super();
    }
    
    /**
     * Constructs a new PythonLookupError with the specified message.
     * 
     * @param message the detail message
     */
    public PythonLookupError(String message) {
        super(message);
    }
    
    /**
     * Constructs a new PythonLookupError with the specified arguments.
     * 
     * @param args the exception arguments
     */
    public PythonLookupError(Object... args) {
        super(args);
    }
    
    /**
     * Constructs a new PythonLookupError with the specified arguments.
     * 
     * @param args the exception arguments
     */
    public PythonLookupError(List<Object> args) {
        super(args);
    }
    
    @Override
    public String getPythonClassName() {
        return "LookupError";
    }
}
