package au.com.allianceautomation.iython.builtins.functions;

import au.com.allianceautomation.iython.builtins.AbstractBuiltinFunction;
import java.util.List;

/**
 * Python __package__ builtin attribute.
 * 
 * This represents the package name for the builtins module.
 * In CPython, this is None for the builtins module since it's not part of a package.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class PackageFunction extends AbstractBuiltinFunction {
    
    public PackageFunction() {
        super("__package__", 0, 0, "__package__ -> str or None");
    }
    
    @Override
    protected Object execute(List<Object> args) {
        // Return None for the builtins module since it's not part of a package
        return null;
    }
}
