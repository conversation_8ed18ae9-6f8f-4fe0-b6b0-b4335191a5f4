{"data_mtime": 1749179627, "dep_lines": [1, 1, 1, 1, 1], "dep_prios": [5, 30, 30, 30, 30], "dependencies": ["builtins", "_frozen_importlib", "_typeshed", "abc", "typing"], "hash": "fb1c7aebe012db5c5fe0143d333e444723c3e4d5", "id": "test_very_simple", "ignore_all": false, "interface_hash": "36852f5a7c150a23d0bf6f04f7be3e4aa3a5da33", "mtime": 1749179626, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\dev\\iython\\test_very_simple.py", "plugin_data": null, "size": 21, "suppressed": [], "version_id": "1.15.0"}