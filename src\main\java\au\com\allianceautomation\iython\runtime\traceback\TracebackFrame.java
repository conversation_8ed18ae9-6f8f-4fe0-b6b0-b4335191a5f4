package au.com.allianceautomation.iython.runtime.traceback;

/**
 * Represents a single frame in a Python traceback.
 * Contains information about the location and context of an execution frame.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class TracebackFrame {
    
    private final String filename;
    private final int lineNumber;
    private final String functionName;
    private final String code;
    
    /**
     * Creates a new traceback frame.
     * 
     * @param filename The name of the file where the frame occurred
     * @param lineNumber The line number in the file
     * @param functionName The name of the function or module
     * @param code The actual code line that was executing
     */
    public TracebackFrame(String filename, int lineNumber, String functionName, String code) {
        this.filename = filename;
        this.lineNumber = lineNumber;
        this.functionName = functionName;
        this.code = code;
    }
    
    /**
     * Gets the filename for this frame.
     * 
     * @return the filename
     */
    public String getFilename() {
        return filename;
    }
    
    /**
     * Gets the line number for this frame.
     * 
     * @return the line number
     */
    public int getLineNumber() {
        return lineNumber;
    }
    
    /**
     * Gets the function name for this frame.
     * 
     * @return the function name
     */
    public String getFunctionName() {
        return functionName;
    }
    
    /**
     * Gets the code line for this frame.
     * 
     * @return the code line
     */
    public String getCode() {
        return code;
    }
    
    /**
     * Formats this frame for display in a traceback.
     * 
     * @return formatted frame string
     */
    public String format() {
        return String.format("  File \"%s\", line %d, in %s%n    %s", 
                           filename, lineNumber, functionName, code);
    }
    
    @Override
    public String toString() {
        return String.format("TracebackFrame(file=%s, line=%d, function=%s)", 
                           filename, lineNumber, functionName);
    }
}
