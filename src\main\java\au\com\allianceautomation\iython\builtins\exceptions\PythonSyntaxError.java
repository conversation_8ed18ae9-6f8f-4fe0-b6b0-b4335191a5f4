package au.com.allianceautomation.iython.builtins.exceptions;

import java.util.List;

/**
 * Raised when the parser encounters a syntax error.
 * This corresponds to Python's SyntaxError.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class PythonSyntaxError extends PythonException {
    
    private static final long serialVersionUID = 1L;
    
    private String filename;
    private Integer lineno;
    private Integer offset;
    private String text;
    private Integer endLineno;
    private Integer endOffset;
    
    /**
     * Constructs a new PythonSyntaxError with no arguments.
     */
    public PythonSyntaxError() {
        super();
    }
    
    /**
     * Constructs a new PythonSyntaxError with the specified message.
     * 
     * @param message the detail message
     */
    public PythonSyntaxError(String message) {
        super(message);
    }
    
    /**
     * Constructs a new PythonSyntaxError with the specified message and details.
     * 
     * @param message the detail message
     * @param filename the name of the file the syntax error occurred in
     * @param lineno the line number where the error occurred
     * @param offset the column where the error occurred
     * @param text the source code text involved in the error
     */
    public PythonSyntaxError(String message, String filename, Integer lineno, Integer offset, String text) {
        super(message);
        this.filename = filename;
        this.lineno = lineno;
        this.offset = offset;
        this.text = text;
    }
    
    /**
     * Constructs a new PythonSyntaxError with the specified arguments.
     * 
     * @param args the exception arguments
     */
    public PythonSyntaxError(Object... args) {
        super(args);
    }
    
    /**
     * Constructs a new PythonSyntaxError with the specified arguments.
     * 
     * @param args the exception arguments
     */
    public PythonSyntaxError(List<Object> args) {
        super(args);
    }
    
    // Getters and setters for syntax error details
    
    public String getFilename() {
        return filename;
    }
    
    public void setFilename(String filename) {
        this.filename = filename;
    }
    
    public Integer getLineno() {
        return lineno;
    }
    
    public void setLineno(Integer lineno) {
        this.lineno = lineno;
    }
    
    public Integer getOffset() {
        return offset;
    }
    
    public void setOffset(Integer offset) {
        this.offset = offset;
    }
    
    public String getText() {
        return text;
    }
    
    public void setText(String text) {
        this.text = text;
    }
    
    public Integer getEndLineno() {
        return endLineno;
    }
    
    public void setEndLineno(Integer endLineno) {
        this.endLineno = endLineno;
    }
    
    public Integer getEndOffset() {
        return endOffset;
    }
    
    public void setEndOffset(Integer endOffset) {
        this.endOffset = endOffset;
    }
    
    @Override
    public String getPythonClassName() {
        return "SyntaxError";
    }
}
