package au.com.allianceautomation.iython.ast.expressions;

import au.com.allianceautomation.iython.ast.ASTVisitor;
import java.util.List;

/**
 * AST node representing set literals and set expressions.
 * Represents expressions like: {1, 2, 3} or {expr for x in iterable}
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class SetExpression extends Expression {
    private final List<Expression> elements;
    
    public SetExpression(int line, int column, List<Expression> elements) {
        super(line, column);
        this.elements = elements;
    }
    
    public List<Expression> getElements() { 
        return elements; 
    }
    
    @Override
    public <T> T accept(ASTVisitor<T> visitor) {
        return visitor.visitSet(this);
    }
    
    @Override
    public String toString() {
        return "SetExpression(" + elements + ")";
    }
}
