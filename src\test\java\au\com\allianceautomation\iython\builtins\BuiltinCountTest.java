package au.com.allianceautomation.iython.builtins;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

import java.util.Set;

/**
 * Test to check the total number of builtin functions implemented.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class BuiltinCountTest {
    
    @Test
    @DisplayName("Test builtin function count")
    void testBuiltinCount() {
        int count = BuiltinRegistry.getBuiltinCount();
        Set<String> names = BuiltinRegistry.getBuiltinNames();
        
        System.out.println("Total builtin functions implemented: " + count);
        System.out.println("Builtin function names:");
        names.stream().sorted().forEach(name -> System.out.println("  " + name));
        
        // We should have significantly more than the original count
        assertTrue(count >= 40, "Should have at least 40 builtin functions, but got: " + count);
    }
}
