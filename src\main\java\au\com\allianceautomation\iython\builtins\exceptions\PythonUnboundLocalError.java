package au.com.allianceautomation.iython.builtins.exceptions;

import java.util.List;

/**
 * Raised when a reference is made to a local variable in a function or method, 
 * but no value has been bound to that variable.
 * This corresponds to Python's UnboundLocalError.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class PythonUnboundLocalError extends PythonNameError {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * Constructs a new PythonUnboundLocalError with no arguments.
     */
    public PythonUnboundLocalError() {
        super();
    }
    
    /**
     * Constructs a new PythonUnboundLocalError with the specified message.
     * 
     * @param message the detail message
     */
    public PythonUnboundLocalError(String message) {
        super(message);
    }
    
    /**
     * Constructs a new PythonUnboundLocalError with the specified message and name.
     * 
     * @param message the detail message
     * @param name the name that could not be found
     */
    public PythonUnboundLocalError(String message, String name) {
        super(message, name);
    }
    
    /**
     * Constructs a new PythonUnboundLocalError with the specified arguments.
     * 
     * @param args the exception arguments
     */
    public PythonUnboundLocalError(Object... args) {
        super(args);
    }
    
    /**
     * Constructs a new PythonUnboundLocalError with the specified arguments.
     * 
     * @param args the exception arguments
     */
    public PythonUnboundLocalError(List<Object> args) {
        super(args);
    }
    
    @Override
    public String getPythonClassName() {
        return "UnboundLocalError";
    }
}
