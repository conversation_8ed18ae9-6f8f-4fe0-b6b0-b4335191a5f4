package au.com.allianceautomation.iython.builtins.functions;

import au.com.allianceautomation.iython.builtins.AbstractBuiltinFunction;
import java.util.List;

/**
 * Python __name__ builtin attribute.
 * 
 * This represents the name of the builtins module.
 * In CPython, this is always 'builtins'.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class NameFunction extends AbstractBuiltinFunction {
    
    public NameFunction() {
        super("__name__", 0, 0, "__name__ -> str");
    }
    
    @Override
    protected Object execute(List<Object> args) {
        // Return the name of the builtins module
        return "builtins";
    }
}
