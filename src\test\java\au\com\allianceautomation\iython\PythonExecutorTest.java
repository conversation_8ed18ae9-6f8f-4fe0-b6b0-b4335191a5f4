package au.com.allianceautomation.iython;

import org.junit.jupiter.api.AfterEach;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

/**
 * Unit tests for PythonExecutor class.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
class PythonExecutorTest {
    
    private PythonExecutor executor;
    
    @BeforeEach
    void setUp() {
        executor = new PythonExecutor();
    }
    
    @AfterEach
    void tearDown() {
        if (executor != null) {
            executor.close();
        }
    }
    
    @Test
    @DisplayName("Execute simple Python print statement")
    void testExecuteSimplePrint() throws PythonExecutionException {
        String code = "print('Hello from Python!')";
        String output = executor.executeCode(code);
        
        assertNotNull(output);
        assertTrue(output.contains("Hello from Python!"));
    }
    
    @Test
    @DisplayName("Execute Python arithmetic operations")
    void testExecuteArithmetic() throws PythonExecutionException {
        String code = "result = 10 + 5 * 2\n" +
                      "print(\"Result: \" + str(result))";

        String output = executor.executeCode(code);

        assertNotNull(output);
        assertTrue(output.contains("Result: 20"));
    }
    
    @Test
    @DisplayName("Execute Python list operations")
    void testExecuteListOperations() throws PythonExecutionException {
        String code = "numbers = [1, 2, 3, 4, 5]\n" +
                      "squared = [x**2 for x in numbers]\n" +
                      "print(\"Squared: \" + str(squared))";

        String output = executor.executeCode(code);

        assertNotNull(output);
        // Power operations return floating-point numbers
        assertTrue(output.contains("[1.0, 4.0, 9.0, 16.0, 25.0]"));
    }
    
    @Test
    @DisplayName("Execute and retrieve variable value")
    void testExecuteAndGetVariable() throws PythonExecutionException {
        String code = "x = 42\n" +
                      "y = \"Hello World\"\n" +
                      "z = [1, 2, 3]";

        executor.executeCode(code);

        Object x = executor.executeAndGetVariable("", "x");
        Object y = executor.executeAndGetVariable("", "y");
        Object z = executor.executeAndGetVariable("", "z");

        assertEquals(42L, x); // Numbers are parsed as Long
        assertEquals("Hello World", y);
        assertNotNull(z);
    }
    
    @Test
    @DisplayName("Set variable from Java and use in Python")
    void testSetVariable() throws PythonExecutionException {
        executor.setVariable("java_value", 100);

        String code = "result = java_value * 2\n" +
                      "print(\"Java value doubled: \" + str(result))";

        String output = executor.executeCode(code);

        assertNotNull(output);
        assertTrue(output.contains("Java value doubled: 200"));
    }
    
    @Test
    @DisplayName("Execute Python function definition and call")
    void testExecuteFunction() throws PythonExecutionException {
        String code = "def greet(name):\n" +
                      "    return \"Hello, \" + name + \"!\"\n" +
                      "\n" +
                      "message = greet(\"iython\")\n" +
                      "print(message)";

        String output = executor.executeCode(code);

        assertNotNull(output);
        assertTrue(output.contains("Hello, iython!"));
    }
    
    @Test
    @DisplayName("Execute Python file")
    void testExecuteFile() throws PythonExecutionException {
        String output = executor.executeFile("hello_world.py");
        
        assertNotNull(output);
        assertTrue(output.contains("Hello, World from Python!"));
        assertTrue(output.contains("Java Virtual Machine"));
    }
    
    @Test
    @DisplayName("Handle Python execution error")
    void testExecutionError() {
        String invalidCode = "print(undefined_variable)";
        
        assertThrows(PythonExecutionException.class, () -> {
            executor.executeCode(invalidCode);
        });
    }
    
    @Test
    @DisplayName("Handle file not found error")
    void testFileNotFound() {
        assertThrows(PythonExecutionException.class, () -> {
            executor.executeFile("nonexistent_file.py");
        });
    }
}
