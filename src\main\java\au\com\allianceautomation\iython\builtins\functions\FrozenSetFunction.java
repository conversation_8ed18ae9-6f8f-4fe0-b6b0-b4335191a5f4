package au.com.allianceautomation.iython.builtins.functions;

import au.com.allianceautomation.iython.builtins.AbstractBuiltinFunction;
import java.util.List;
import java.util.Set;
import java.util.HashSet;
import java.util.Collections;

/**
 * Python frozenset() builtin function.
 * 
 * Returns an immutable frozenset object constructed from an optional iterable.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class FrozenSetFunction extends AbstractBuiltinFunction {
    
    public FrozenSetFunction() {
        super("frozenset", 0, 1, "frozenset([iterable]) -> frozenset");
    }
    
    @Override
    protected Object execute(List<Object> args) {
        if (args.isEmpty()) {
            // frozenset() -> empty frozenset
            return new PythonFrozenSet(new HashSet<>());
        }
        
        Object iterable = args.get(0);
        Set<Object> elements = new HashSet<>();
        
        if (iterable instanceof List) {
            List<?> list = (List<?>) iterable;
            for (Object item : list) {
                elements.add(item);
            }
        } else if (iterable instanceof Set) {
            Set<?> set = (Set<?>) iterable;
            for (Object item : set) {
                elements.add(item);
            }
        } else if (iterable instanceof String) {
            String str = (String) iterable;
            for (char c : str.toCharArray()) {
                elements.add(String.valueOf(c));
            }
        } else if (iterable.getClass().isArray()) {
            Object[] array = (Object[]) iterable;
            for (Object item : array) {
                elements.add(item);
            }
        } else if (iterable instanceof PythonFrozenSet) {
            // Copy constructor
            PythonFrozenSet other = (PythonFrozenSet) iterable;
            elements.addAll(other.getElements());
        } else {
            throw new RuntimeException("TypeError: '" + iterable.getClass().getSimpleName() + 
                                     "' object is not iterable");
        }
        
        return new PythonFrozenSet(elements);
    }
    
    /**
     * Immutable frozenset object for Python.
     */
    public static class PythonFrozenSet {
        private final Set<Object> elements;
        
        public PythonFrozenSet(Set<Object> elements) {
            this.elements = Collections.unmodifiableSet(new HashSet<>(elements));
        }
        
        public Set<Object> getElements() {
            return elements;
        }
        
        public int size() {
            return elements.size();
        }
        
        public boolean contains(Object item) {
            return elements.contains(item);
        }
        
        public boolean isEmpty() {
            return elements.isEmpty();
        }
        
        public PythonFrozenSet union(PythonFrozenSet other) {
            Set<Object> result = new HashSet<>(elements);
            result.addAll(other.elements);
            return new PythonFrozenSet(result);
        }
        
        public PythonFrozenSet intersection(PythonFrozenSet other) {
            Set<Object> result = new HashSet<>(elements);
            result.retainAll(other.elements);
            return new PythonFrozenSet(result);
        }
        
        public PythonFrozenSet difference(PythonFrozenSet other) {
            Set<Object> result = new HashSet<>(elements);
            result.removeAll(other.elements);
            return new PythonFrozenSet(result);
        }
        
        public boolean isSubset(PythonFrozenSet other) {
            return other.elements.containsAll(elements);
        }
        
        public boolean isSuperset(PythonFrozenSet other) {
            return elements.containsAll(other.elements);
        }
        
        public boolean isDisjoint(PythonFrozenSet other) {
            return Collections.disjoint(elements, other.elements);
        }
        
        @Override
        public String toString() {
            if (elements.isEmpty()) {
                return "frozenset()";
            }
            
            StringBuilder sb = new StringBuilder("frozenset({");
            boolean first = true;
            for (Object element : elements) {
                if (!first) {
                    sb.append(", ");
                }
                if (element instanceof String) {
                    sb.append("'").append(element).append("'");
                } else {
                    sb.append(element);
                }
                first = false;
            }
            sb.append("})");
            return sb.toString();
        }
        
        @Override
        public boolean equals(Object obj) {
            if (this == obj) return true;
            if (!(obj instanceof PythonFrozenSet)) return false;
            PythonFrozenSet other = (PythonFrozenSet) obj;
            return elements.equals(other.elements);
        }
        
        @Override
        public int hashCode() {
            return elements.hashCode();
        }
    }
}
