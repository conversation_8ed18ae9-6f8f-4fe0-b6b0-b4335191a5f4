package au.com.allianceautomation.iython.builtins.exceptions;

import java.util.List;

/**
 * Raised when an operation or function receives an argument that has the right type 
 * but an inappropriate value, and the situation is not described by a more precise 
 * exception such as IndexError.
 * This corresponds to Python's ValueError.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class PythonValueError extends PythonException {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * Constructs a new PythonValueError with no arguments.
     */
    public PythonValueError() {
        super();
    }
    
    /**
     * Constructs a new PythonValueError with the specified message.
     * 
     * @param message the detail message
     */
    public PythonValueError(String message) {
        super(message);
    }
    
    /**
     * Constructs a new PythonValueError with the specified arguments.
     * 
     * @param args the exception arguments
     */
    public PythonValueError(Object... args) {
        super(args);
    }
    
    /**
     * Constructs a new PythonValueError with the specified arguments.
     * 
     * @param args the exception arguments
     */
    public PythonValueError(List<Object> args) {
        super(args);
    }
    
    @Override
    public String getPythonClassName() {
        return "ValueError";
    }
}
