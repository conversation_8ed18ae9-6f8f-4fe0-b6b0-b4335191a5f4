package au.com.allianceautomation.iython.builtins;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

import au.com.allianceautomation.iython.builtins.exceptions.PythonAssertionError;
import au.com.allianceautomation.iython.builtins.exceptions.PythonAttributeError;
import au.com.allianceautomation.iython.builtins.exceptions.PythonBaseException;
import au.com.allianceautomation.iython.builtins.exceptions.PythonException;
import au.com.allianceautomation.iython.builtins.exceptions.PythonImportError;
import au.com.allianceautomation.iython.builtins.exceptions.PythonKeyboardInterrupt;
import au.com.allianceautomation.iython.builtins.exceptions.PythonNameError;
import au.com.allianceautomation.iython.builtins.exceptions.PythonRuntimeError;
import au.com.allianceautomation.iython.builtins.exceptions.PythonSyntaxError;
import au.com.allianceautomation.iython.builtins.exceptions.PythonSystemExit;
import au.com.allianceautomation.iython.builtins.exceptions.PythonTypeError;
import au.com.allianceautomation.iython.builtins.exceptions.PythonValueError;
import au.com.allianceautomation.iython.builtins.exceptions.arithmetic.PythonArithmeticError;
import au.com.allianceautomation.iython.builtins.exceptions.arithmetic.PythonOverflowError;
import au.com.allianceautomation.iython.builtins.exceptions.arithmetic.PythonZeroDivisionError;
import au.com.allianceautomation.iython.builtins.exceptions.lookup.PythonIndexError;
import au.com.allianceautomation.iython.builtins.exceptions.lookup.PythonKeyError;
import au.com.allianceautomation.iython.builtins.exceptions.lookup.PythonLookupError;

/**
 * Registry for all Python builtin exceptions.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class ExceptionRegistry {
    
    private static final Map<String, Class<? extends PythonBaseException>> exceptions = new HashMap<>();
    
    static {
        // Initialize all builtin exceptions
        
        // Base exceptions
        registerException("BaseException", PythonBaseException.class);
        registerException("Exception", PythonException.class);
        
        // Arithmetic exceptions
        registerException("ArithmeticError", PythonArithmeticError.class);

        registerException("OverflowError", PythonOverflowError.class);
        registerException("ZeroDivisionError", PythonZeroDivisionError.class);
        
        // Lookup exceptions
        registerException("LookupError", PythonLookupError.class);
        registerException("IndexError", PythonIndexError.class);
        registerException("KeyError", PythonKeyError.class);
        
        // Core exceptions
        registerException("AssertionError", PythonAssertionError.class);
        registerException("AttributeError", PythonAttributeError.class);
        registerException("ImportError", PythonImportError.class);
        registerException("NameError", PythonNameError.class);
        registerException("RuntimeError", PythonRuntimeError.class);
        registerException("SyntaxError", PythonSyntaxError.class);
        registerException("TypeError", PythonTypeError.class);
        registerException("ValueError", PythonValueError.class);
        
        // System exceptions
        registerException("KeyboardInterrupt", PythonKeyboardInterrupt.class);
        registerException("SystemExit", PythonSystemExit.class);
    }
    
    /**
     * Register a builtin exception.
     * 
     * @param name The exception name
     * @param exceptionClass The exception class
     */
    private static void registerException(String name, Class<? extends PythonBaseException> exceptionClass) {
        exceptions.put(name, exceptionClass);
    }
    
    /**
     * Get an exception class by name.
     * 
     * @param name The name of the exception
     * @return The exception class, or null if not found
     */
    public static Class<? extends PythonBaseException> getException(String name) {
        return exceptions.get(name);
    }
    
    /**
     * Check if a name is a builtin exception.
     * 
     * @param name The name to check
     * @return true if it's a builtin exception
     */
    public static boolean isException(String name) {
        return exceptions.containsKey(name);
    }
    
    /**
     * Get all builtin exception names.
     * 
     * @return A set of all builtin exception names
     */
    public static Set<String> getExceptionNames() {
        return exceptions.keySet();
    }
    
    /**
     * Get the total number of registered builtin exceptions.
     * 
     * @return The number of builtin exceptions
     */
    public static int getExceptionCount() {
        return exceptions.size();
    }
    
    /**
     * Create a new instance of the specified exception.
     * 
     * @param name The exception name
     * @param message The exception message
     * @return A new exception instance, or null if the exception is not found
     */
    public static PythonBaseException createException(String name, String message) {
        Class<? extends PythonBaseException> exceptionClass = getException(name);
        if (exceptionClass == null) {
            return null;
        }
        
        try {
            return exceptionClass.getConstructor(String.class).newInstance(message);
        } catch (Exception e) {
            // Fallback to default constructor
            try {
                return exceptionClass.getConstructor().newInstance();
            } catch (Exception ex) {
                return null;
            }
        }
    }
    
    /**
     * Create a new instance of the specified exception.
     * 
     * @param name The exception name
     * @param args The exception arguments
     * @return A new exception instance, or null if the exception is not found
     */
    public static PythonBaseException createException(String name, Object... args) {
        Class<? extends PythonBaseException> exceptionClass = getException(name);
        if (exceptionClass == null) {
            return null;
        }
        
        try {
            return exceptionClass.getConstructor(Object[].class).newInstance((Object) args);
        } catch (Exception e) {
            // Fallback to string constructor if available
            if (args.length > 0) {
                try {
                    return exceptionClass.getConstructor(String.class).newInstance(String.valueOf(args[0]));
                } catch (Exception ex) {
                    // Fallback to default constructor
                    try {
                        return exceptionClass.getConstructor().newInstance();
                    } catch (Exception exc) {
                        return null;
                    }
                }
            } else {
                try {
                    return exceptionClass.getConstructor().newInstance();
                } catch (Exception ex) {
                    return null;
                }
            }
        }
    }
}
