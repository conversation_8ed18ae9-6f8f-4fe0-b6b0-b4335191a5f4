package au.com.allianceautomation.iython.builtins.exceptions.arithmetic;

import java.util.List;

/**
 * Not currently used in standard Python, but available for compatibility.
 * This corresponds to Python's FloatingPointError.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class PythonFloatingPointError extends PythonArithmeticError {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * Constructs a new PythonFloatingPointError with no arguments.
     */
    public PythonFloatingPointError() {
        super();
    }
    
    /**
     * Constructs a new PythonFloatingPointError with the specified message.
     * 
     * @param message the detail message
     */
    public PythonFloatingPointError(String message) {
        super(message);
    }
    
    /**
     * Constructs a new PythonFloatingPointError with the specified arguments.
     * 
     * @param args the exception arguments
     */
    public PythonFloatingPointError(Object... args) {
        super(args);
    }
    
    /**
     * Constructs a new PythonFloatingPointError with the specified arguments.
     * 
     * @param args the exception arguments
     */
    public PythonFloatingPointError(List<Object> args) {
        super(args);
    }
    
    @Override
    public String getPythonClassName() {
        return "FloatingPointError";
    }
}
