package au.com.allianceautomation.iython.builtins.functions;

import java.util.List;

import au.com.allianceautomation.iython.builtins.AbstractBuiltinFunction;

/**
 * Python any() builtin function.
 * 
 * Returns True if any element of the iterable is true. If the iterable is empty, returns False.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class AnyFunction extends AbstractBuiltinFunction {
    
    public AnyFunction() {
        super("any", 1, 1, "any(iterable) -> bool");
    }
    
    @Override
    protected Object execute(List<Object> args) {
        Object iterable = args.get(0);
        
        if (iterable instanceof List) {
            List<?> list = (List<?>) iterable;
            for (Object item : list) {
                if (isTruthy(item)) {
                    return true;
                }
            }
            return false;
        } else if (iterable instanceof String) {
            String str = (String) iterable;
            for (char c : str.toCharArray()) {
                if (isTruthy(String.valueOf(c))) {
                    return true;
                }
            }
            return false;
        } else if (iterable.getClass().isArray()) {
            Object[] array = (Object[]) iterable;
            for (Object item : array) {
                if (isTruthy(item)) {
                    return true;
                }
            }
            return false;
        } else {
            throw new RuntimeException("'" + iterable.getClass().getSimpleName() + "' object is not iterable");
        }
    }
}
