package au.com.allianceautomation.iython.runtime.traceback;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import static org.junit.jupiter.api.Assertions.*;

/**
 * Debug test for NameSuggester to understand why it's not working.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
class NameSuggesterDebugTest {
    
    @Test
    @DisplayName("Debug name suggestion algorithm")
    void debugNameSuggestion() {
        java.util.Set<String> availableNames = java.util.Set.of("vars", "print", "len", "str");
        
        System.out.println("Available names: " + availableNames);
        
        // Test the similarity calculation directly
        String undefinedName = "cars";
        System.out.println("Looking for suggestions for: " + undefinedName);
        
        for (String name : availableNames) {
            double similarity = calculateSimilarity(undefinedName, name);
            System.out.println("Similarity between '" + undefinedName + "' and '" + name + "': " + similarity);
        }
        
        String suggestion = NameSuggester.suggestSimilarName(undefinedName, availableNames);
        System.out.println("Final suggestion: " + suggestion);
        
        assertNotNull(suggestion, "Should find a suggestion for 'cars'");
    }
    
    // Copy the similarity calculation method to debug it
    private static double calculateSimilarity(String s1, String s2) {
        if (s1.equals(s2)) {
            return 1.0;
        }
        
        // Use a combination of different similarity measures
        double levenshteinSim = 1.0 - (double) levenshteinDistance(s1, s2) / Math.max(s1.length(), s2.length());
        double jaccardSim = jaccardSimilarity(s1, s2);
        double prefixSim = commonPrefixSimilarity(s1, s2);
        
        System.out.println("  Levenshtein: " + levenshteinSim + ", Jaccard: " + jaccardSim + ", Prefix: " + prefixSim);
        
        // Weight the different measures
        return 0.5 * levenshteinSim + 0.3 * jaccardSim + 0.2 * prefixSim;
    }
    
    private static int levenshteinDistance(String s1, String s2) {
        int[][] dp = new int[s1.length() + 1][s2.length() + 1];
        
        for (int i = 0; i <= s1.length(); i++) {
            dp[i][0] = i;
        }
        for (int j = 0; j <= s2.length(); j++) {
            dp[0][j] = j;
        }
        
        for (int i = 1; i <= s1.length(); i++) {
            for (int j = 1; j <= s2.length(); j++) {
                if (s1.charAt(i - 1) == s2.charAt(j - 1)) {
                    dp[i][j] = dp[i - 1][j - 1];
                } else {
                    dp[i][j] = 1 + Math.min(Math.min(dp[i - 1][j], dp[i][j - 1]), dp[i - 1][j - 1]);
                }
            }
        }
        
        return dp[s1.length()][s2.length()];
    }
    
    private static double jaccardSimilarity(String s1, String s2) {
        if (s1.length() < 2 || s2.length() < 2) {
            return s1.equals(s2) ? 1.0 : 0.0;
        }
        
        java.util.Set<String> bigrams1 = getBigrams(s1);
        java.util.Set<String> bigrams2 = getBigrams(s2);
        
        java.util.Set<String> intersection = new java.util.HashSet<>(bigrams1);
        intersection.retainAll(bigrams2);
        
        java.util.Set<String> union = new java.util.HashSet<>(bigrams1);
        union.addAll(bigrams2);
        
        return union.isEmpty() ? 0.0 : (double) intersection.size() / union.size();
    }
    
    private static java.util.Set<String> getBigrams(String s) {
        java.util.Set<String> bigrams = new java.util.HashSet<>();
        for (int i = 0; i < s.length() - 1; i++) {
            bigrams.add(s.substring(i, i + 2));
        }
        return bigrams;
    }
    
    private static double commonPrefixSimilarity(String s1, String s2) {
        int commonLength = 0;
        int minLength = Math.min(s1.length(), s2.length());
        
        for (int i = 0; i < minLength; i++) {
            if (s1.charAt(i) == s2.charAt(i)) {
                commonLength++;
            } else {
                break;
            }
        }
        
        return (double) commonLength / Math.max(s1.length(), s2.length());
    }
}
